@echo off

echo ===================================
echo  Ice Cream Shop - 啟動器
echo ===================================
echo.
echo [1] 啟動後端伺服器 (Node.js + SQLite)
echo [2] 啟動完整系統 (後端 + 前端 Live Server)
echo [3] 開啟客戶端 App (customer-app.html)
echo [4] 開啟管理後台 (admin-panel.html)
echo.
set /p choice="請輸入您的選擇 [1-4]: "

if "%choice%"=="1" (
    echo 正在啟動後端伺服器...
    cd backend
    npm start
) else if "%choice%"=="2" (
    echo 正在啟動完整系統...
    start "Backend" cmd /c "cd backend && npm start"
    start "Frontend" cmd /c "npx live-server --port=8080 --open=index.html"
) else if "%choice%"=="3" (
    echo 正在開啟客戶端 App...
    start "" customer-app.html
) else if "%choice%"=="4" (
    echo 正在開啟管理後台...
    start "" admin-panel.html
) else (
    echo 無效的選擇
)

echo.
pause   