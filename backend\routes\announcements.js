const express = require('express');
const router = express.Router();
const { Announcement } = require('../models');
const { adminAuthMiddleware } = require('./auth');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

// 獲取所有公告（管理員用，包含非活躍公告）
router.get('/admin', adminAuthMiddleware, async (req, res) => {
  try {
    const announcements = await Announcement.findAll({
      order: [
        ['priority', 'DESC'],
        ['created_at', 'DESC']
      ]
    });
    res.json(announcements);
  } catch (error) {
    console.error('獲取公告列表錯誤:', error);
    res.status(500).json({ message: '獲取公告列表失敗' });
  }
});

// 獲取活躍公告（公開，用於前台顯示）
router.get('/active', async (req, res) => {
  try {
    const now = new Date();
    
    const announcements = await Announcement.findAll({
      where: {
        is_active: true,
        [Op.or]: [
          {
            start_date: null,
            end_date: null
          },
          {
            start_date: { [Op.lte]: now },
            end_date: null
          },
          {
            start_date: null,
            end_date: { [Op.gte]: now }
          },
          {
            start_date: { [Op.lte]: now },
            end_date: { [Op.gte]: now }
          }
        ]
      },
      order: [
        ['priority', 'DESC'],
        ['created_at', 'DESC']
      ]
    });
    
    res.json(announcements);
  } catch (error) {
    console.error('獲取活躍公告錯誤:', error);
    res.status(500).json({ message: '獲取公告失敗' });
  }
});

// 獲取單一公告（需要管理員權限）
router.get('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const announcement = await Announcement.findByPk(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    res.json(announcement);
  } catch (error) {
    console.error('獲取公告錯誤:', error);
    res.status(500).json({ message: '獲取公告失敗' });
  }
});

// 創建新公告（需要管理員權限）
router.post('/', adminAuthMiddleware, async (req, res) => {
  try {
    const {
      title,
      content,
      type = 'info',
      is_active = true,
      priority = 0,
      start_date,
      end_date
    } = req.body;
    
    if (!title || !content) {
      return res.status(400).json({ message: '標題和內容為必填項' });
    }
    
    const announcement = await Announcement.create({
      title,
      content,
      type,
      is_active,
      priority,
      start_date: start_date || null,
      end_date: end_date || null
    });
    
    res.status(201).json(announcement);
  } catch (error) {
    console.error('創建公告錯誤:', error);
    res.status(400).json({ message: '創建公告失敗: ' + error.message });
  }
});

// 更新公告（需要管理員權限）
router.put('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const announcement = await Announcement.findByPk(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    const {
      title,
      content,
      type,
      is_active,
      priority,
      start_date,
      end_date
    } = req.body;
    
    await announcement.update({
      title: title || announcement.title,
      content: content || announcement.content,
      type: type || announcement.type,
      is_active: is_active !== undefined ? is_active : announcement.is_active,
      priority: priority !== undefined ? priority : announcement.priority,
      start_date: start_date !== undefined ? (start_date || null) : announcement.start_date,
      end_date: end_date !== undefined ? (end_date || null) : announcement.end_date
    });
    
    res.json(announcement);
  } catch (error) {
    console.error('更新公告錯誤:', error);
    res.status(400).json({ message: '更新公告失敗: ' + error.message });
  }
});

// 刪除公告（需要管理員權限）
router.delete('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const announcement = await Announcement.findByPk(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    await announcement.destroy();
    res.json({ message: '公告已刪除' });
  } catch (error) {
    console.error('刪除公告錯誤:', error);
    res.status(500).json({ message: '刪除公告失敗' });
  }
});

// 切換公告狀態（需要管理員權限）
router.patch('/:id/toggle', adminAuthMiddleware, async (req, res) => {
  try {
    const announcement = await Announcement.findByPk(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    await announcement.update({
      is_active: !announcement.is_active
    });
    
    res.json(announcement);
  } catch (error) {
    console.error('切換公告狀態錯誤:', error);
    res.status(500).json({ message: '切換狀態失敗' });
  }
});

// 批量更新優先級（需要管理員權限）
router.post('/reorder', adminAuthMiddleware, async (req, res) => {
  try {
    const { orders } = req.body; // [{ id: 1, priority: 10 }, ...]
    
    if (!Array.isArray(orders)) {
      return res.status(400).json({ message: '請提供正確的排序資料' });
    }
    
    // 使用事務確保一致性
    const result = await sequelize.transaction(async (t) => {
      for (const order of orders) {
        await Announcement.update(
          { priority: order.priority },
          {
            where: { id: order.id },
            transaction: t
          }
        );
      }
      return true;
    });
    
    res.json({ message: '優先級更新成功' });
  } catch (error) {
    console.error('更新優先級錯誤:', error);
    res.status(500).json({ message: '更新優先級失敗' });
  }
});

module.exports = router; 