const { Sequelize } = require('sequelize');
const path = require('path');

// SQLite 資料庫配置
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(__dirname, '..', 'database', 'ice_cream_shop.db'),
  logging: false, // 設為 true 可以看到 SQL 查詢
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
});

// 測試資料庫連接
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ SQLite 資料庫連接成功！');
  } catch (error) {
    console.error('❌ 資料庫連接失敗:', error);
  }
};

module.exports = {
  sequelize,
  testConnection
}; 