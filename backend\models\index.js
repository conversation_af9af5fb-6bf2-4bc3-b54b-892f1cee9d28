const { sequelize } = require('../config/database');
const User = require('./User');
const Product = require('./Product');
const Order = require('./Order');
const Admin = require('./Admin');
const Announcement = require('./Announcement');
const Discount = require('./Discount');

// 定義模型關聯
// Order 和 Product 的多對多關係通過 items JSON 欄位處理
// 不需要額外的關聯表

// 同步所有模型到資料庫
const syncDatabase = async () => {
  try {
    // 開發環境下使用 alter: true 自動調整表結構
    // 生產環境應該使用 migrations
    await sequelize.sync({ alter: true });
    console.log('✅ 資料庫同步成功！');
    
    // 創建預設管理員帳號
    await Admin.createDefaultAdmin();
  } catch (error) {
    console.error('❌ 資料庫同步失敗:', error);
  }
};

module.exports = {
  sequelize,
  User,
  Product,
  Order,
  Admin,
  Announcement,
  Discount,
  syncDatabase
}; 