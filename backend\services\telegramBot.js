const TelegramBot = require('node-telegram-bot-api');
const fs = require('fs').promises;
const path = require('path');

// 配置檔案路徑
const CONFIG_PATH = path.join(__dirname, '../config/telegram.json');

// 確保配置目錄存在
const ensureConfigDir = async () => {
  const configDir = path.dirname(CONFIG_PATH);
  try {
    await fs.access(configDir);
  } catch {
    await fs.mkdir(configDir, { recursive: true });
  }
};

class TelegramBotService {
  constructor() {
    this.bot = null;
    this.chatId = null;
    this.isEnabled = false;
    this.loadConfig();
  }

  // 載入配置
  async loadConfig() {
    try {
      const configData = await fs.readFile(CONFIG_PATH, 'utf8');
      const config = JSON.parse(configData);
      
      if (config.token && config.chatId) {
        this.initBot(config.token, config.chatId);
        this.isEnabled = config.enabled || false;
      }
    } catch (error) {
      console.log('📱 Telegram Bot 配置未找到或無效');
    }
  }

  // 保存配置
  async saveConfig(token, chatId, enabled = true) {
    try {
      await ensureConfigDir();
      const config = { token, chatId, enabled };
      await fs.writeFile(CONFIG_PATH, JSON.stringify(config, null, 2));
      
      // 重新初始化 Bot
      this.initBot(token, chatId);
      this.isEnabled = enabled;
      
      return { success: true, message: '配置保存成功' };
    } catch (error) {
      console.error('保存 Telegram 配置失敗:', error);
      return { success: false, message: '保存配置失敗' };
    }
  }

  // 初始化 Bot
  initBot(token, chatId) {
    try {
      // 如果已有 Bot 實例，先停止
      if (this.bot) {
        this.bot.stopPolling();
      }
      
      this.bot = new TelegramBot(token, { polling: false });
      this.chatId = chatId;
      console.log('✅ Telegram Bot 初始化成功');
    } catch (error) {
      console.error('❌ Telegram Bot 初始化失敗:', error.message);
      this.bot = null;
    }
  }

  // 發送訊息
  async sendMessage(message) {
    if (!this.isEnabled || !this.bot || !this.chatId) {
      return;
    }

    try {
      await this.bot.sendMessage(this.chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
    } catch (error) {
      console.error('發送 Telegram 訊息失敗:', error.message);
    }
  }

  // 發送新訂單通知
  async sendNewOrderNotification(order) {
    const message = `
🎉 <b>新訂單通知</b>

📋 訂單編號：<code>${order.order_number}</code>
👤 客戶姓名：${order.customer_name}
📱 客戶電話：${order.customer_phone}
🏪 取貨門市：${order.seven_store_name}

🍦 <b>訂購商品：</b>
${this.formatOrderItems(order.items)}

💰 <b>總金額：</b>NT$ ${order.total_amount}
📝 <b>備註：</b>${order.notes || '無'}

⏰ 訂單時間：${new Date(order.created_at).toLocaleString('zh-TW')}
`;

    await this.sendMessage(message);
  }

  // 發送訂單狀態更新通知
  async sendOrderStatusUpdate(order, oldStatus, newStatus) {
    const statusMap = {
      'pending': '待確認',
      'confirmed': '已確認',
      'preparing': '製作中',
      'ready': '待取貨',
      'delivered': '已完成',
      'cancelled': '已取消'
    };

    const message = `
🔄 <b>訂單狀態更新</b>

📋 訂單編號：<code>${order.order_number}</code>
👤 客戶：${order.customer_name}

📊 狀態變更：
${statusMap[oldStatus] || oldStatus} ➡️ ${statusMap[newStatus] || newStatus}

${newStatus === 'cancelled' ? '❌ 訂單已取消，庫存已恢復' : ''}
${newStatus === 'ready' ? '✅ 商品已準備完成，請通知客戶取貨' : ''}
`;

    await this.sendMessage(message);
  }

  // 發送付款狀態更新通知
  async sendPaymentStatusUpdate(order, newStatus) {
    const statusMap = {
      'pending': '待付款',
      'paid': '已付款',
      'refunded': '已退款'
    };

    const message = `
💳 <b>付款狀態更新</b>

📋 訂單編號：<code>${order.order_number}</code>
👤 客戶：${order.customer_name}
💰 金額：NT$ ${order.total_amount}

📊 付款狀態：${statusMap[newStatus] || newStatus}
`;

    await this.sendMessage(message);
  }

  // 發送每日營業報告
  async sendDailyReport(stats) {
    const message = `
📊 <b>今日營業報告</b>

📅 日期：${new Date().toLocaleDateString('zh-TW')}

📈 <b>營業統計：</b>
• 今日訂單數：${stats.todayOrders} 筆
• 今日營收：NT$ ${stats.todayRevenue}
• 待處理訂單：${stats.pendingOrders} 筆
• 本月訂單數：${stats.monthOrders} 筆

💡 記得查看後台管理系統了解詳細資訊！
`;

    await this.sendMessage(message);
  }

  // 格式化訂單項目
  formatOrderItems(items) {
    if (!items || items.length === 0) return '無商品資訊';
    
    return items.map((item, index) => {
      const toppings = item.toppings && item.toppings.length > 0 
        ? ` + ${item.toppings.join(', ')}` 
        : '';
      return `${index + 1}. ${item.baseName} x${item.quantity}${toppings} - NT$ ${item.itemPrice}`;
    }).join('\n');
  }

  // 測試連接
  async testConnection() {
    if (!this.bot || !this.chatId) {
      return { success: false, message: 'Bot 未配置' };
    }

    try {
      await this.bot.sendMessage(this.chatId, 
        '✅ Telegram Bot 連接測試成功！\n\n' +
        '這是來自「海水不可斗量」冰淇淋店管理系統的測試訊息。'
      );
      return { success: true, message: '測試訊息已發送' };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  // 獲取當前配置
  async getConfig() {
    try {
      const configData = await fs.readFile(CONFIG_PATH, 'utf8');
      const config = JSON.parse(configData);
      // 不返回 token 的完整內容
      return {
        hasToken: !!config.token,
        tokenPreview: config.token ? `${config.token.substring(0, 10)}...` : null,
        chatId: config.chatId,
        enabled: config.enabled || false
      };
    } catch (error) {
      return {
        hasToken: false,
        tokenPreview: null,
        chatId: null,
        enabled: false
      };
    }
  }

  // 切換啟用狀態
  async toggleEnabled() {
    try {
      const configData = await fs.readFile(CONFIG_PATH, 'utf8');
      const config = JSON.parse(configData);
      config.enabled = !config.enabled;
      await ensureConfigDir();
      await fs.writeFile(CONFIG_PATH, JSON.stringify(config, null, 2));
      
      this.isEnabled = config.enabled;
      return { success: true, enabled: config.enabled };
    } catch (error) {
      return { success: false, message: '切換狀態失敗' };
    }
  }
}

// 創建單例實例
const telegramBotService = new TelegramBotService();

module.exports = telegramBotService; 