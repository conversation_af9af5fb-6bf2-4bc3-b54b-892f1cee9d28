const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Product = sequelize.define('product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    trim: true
  },
  type: {
    type: DataTypes.ENUM('base', 'topping'),
    allowNull: false
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      requiredForTopping(value) {
        if (this.type === 'topping' && !value) {
          throw new Error('類別是配料時必填');
        }
      }
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
    min: 0
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    defaultValue: [],
    comment: '支援1-5張圖片'
  },
  stock: {
    type: DataTypes.INTEGER,
    defaultValue: 999,
    allowNull: false
  },
  available: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
});

module.exports = Product; 