// 7-11 門市資料服務
// 注意：實際應用中應使用 7-11 官方 API
// 這裡使用模擬資料

const mockStores = [
  // 台北市
  { id: '100001', name: '北車門市', city: '台北市', district: '中正區', address: '台北市中正區忠孝西路一段49號' },
  { id: '100002', name: '信義門市', city: '台北市', district: '信義區', address: '台北市信義區信義路五段7號' },
  { id: '100003', name: '士林門市', city: '台北市', district: '士林區', address: '台北市士林區中正路235號' },
  { id: '100004', name: '內湖門市', city: '台北市', district: '內湖區', address: '台北市內湖區成功路四段30號' },
  { id: '100005', name: '大安門市', city: '台北市', district: '大安區', address: '台北市大安區復興南路一段107號' },
  
  // 新北市
  { id: '200001', name: '板橋門市', city: '新北市', district: '板橋區', address: '新北市板橋區文化路一段100號' },
  { id: '200002', name: '永和門市', city: '新北市', district: '永和區', address: '新北市永和區中正路200號' },
  { id: '200003', name: '新店門市', city: '新北市', district: '新店區', address: '新北市新店區北新路三段88號' },
  { id: '200004', name: '淡水門市', city: '新北市', district: '淡水區', address: '新北市淡水區中正路100號' },
  { id: '200005', name: '三重門市', city: '新北市', district: '三重區', address: '新北市三重區重新路二段78號' },
  
  // 桃園市
  { id: '300001', name: '桃園門市', city: '桃園市', district: '桃園區', address: '桃園市桃園區中正路50號' },
  { id: '300002', name: '中壢門市', city: '桃園市', district: '中壢區', address: '桃園市中壢區中正路100號' },
  { id: '300003', name: '平鎮門市', city: '桃園市', district: '平鎮區', address: '桃園市平鎮區延平路一段168號' },
  
  // 台中市
  { id: '400001', name: '台中門市', city: '台中市', district: '中區', address: '台中市中區中正路100號' },
  { id: '400002', name: '西屯門市', city: '台中市', district: '西屯區', address: '台中市西屯區台灣大道三段99號' },
  { id: '400003', name: '北屯門市', city: '台中市', district: '北屯區', address: '台中市北屯區崇德路二段168號' },
  
  // 台南市
  { id: '600001', name: '台南門市', city: '台南市', district: '中西區', address: '台南市中西區中正路1號' },
  { id: '600002', name: '東區門市', city: '台南市', district: '東區', address: '台南市東區東門路二段100號' },
  { id: '600003', name: '安平門市', city: '台南市', district: '安平區', address: '台南市安平區安平路500號' },
  
  // 高雄市
  { id: '800001', name: '高雄門市', city: '高雄市', district: '前金區', address: '高雄市前金區中正四路100號' },
  { id: '800002', name: '左營門市', city: '高雄市', district: '左營區', address: '高雄市左營區博愛二路100號' },
  { id: '800003', name: '三民門市', city: '高雄市', district: '三民區', address: '高雄市三民區建國三路100號' }
];

// 獲取所有門市
const getAllStores = () => {
  return mockStores;
};

// 根據城市獲取門市
const getStoresByCity = (city) => {
  return mockStores.filter(store => store.city === city);
};

// 根據門市代號獲取門市資訊
const getStoreById = (storeId) => {
  return mockStores.find(store => store.id === storeId);
};

// 搜尋門市
const searchStores = (keyword) => {
  const lowerKeyword = keyword.toLowerCase();
  return mockStores.filter(store => 
    store.name.toLowerCase().includes(lowerKeyword) ||
    store.address.toLowerCase().includes(lowerKeyword) ||
    store.district.toLowerCase().includes(lowerKeyword)
  );
};

// 獲取城市列表
const getCities = () => {
  const cities = [...new Set(mockStores.map(store => store.city))];
  return cities;
};

// 驗證門市代號
const validateStoreId = (storeId) => {
  return mockStores.some(store => store.id === storeId);
};

module.exports = {
  getAllStores,
  getStoresByCity,
  getStoreById,
  searchStores,
  getCities,
  validateStoreId
}; 