const XLSX = require('xlsx');
const { Op } = require('sequelize');
const { Order, Product } = require('../models');

// 生成檔案名稱：DOC{西元年}{日}{月}
const generateFileName = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `DOC${year}${day}${month}.xlsx`;
};

// 格式化訂單資料為 Excel 格式
const formatOrdersForExcel = (orders) => {
  const excelData = [];
  
  orders.forEach(order => {
    // 解析商品資訊
    let productInfo = '';
    let totalQuantity = 0;
    
    if (order.items && Array.isArray(order.items)) {
      order.items.forEach((item, index) => {
        const baseInfo = `${item.baseName || '未知商品'} x${item.quantity}`;
        const toppings = item.toppings && item.toppings.length > 0
          ? ` (配料: ${item.toppings.join(', ')})`
          : '';
        productInfo += `${index + 1}. ${baseInfo}${toppings}\n`;
        totalQuantity += item.quantity;
      });
    }
    
    // 轉換狀態文字
    const statusMap = {
      'pending': '待確認',
      'confirmed': '已確認',
      'preparing': '製作中',
      'ready': '待取貨',
      'delivered': '已完成',
      'cancelled': '已取消'
    };
    
    const paymentStatusMap = {
      'pending': '待付款',
      'paid': '已付款',
      'refunded': '已退款'
    };
    
    // 建立 Excel 行資料
    excelData.push({
      '訂單編號': order.order_number,
      '訂單日期': formatDate(order.created_at),
      '訂單時間': formatTime(order.created_at),
      '客戶姓名': order.customer_name,
      '客戶電話': order.customer_phone,
      '客戶信箱': order.customer_email,
      '7-11門市代號': order.seven_store_id,
      '7-11門市名稱': order.seven_store_name || '未知門市',
      '商品明細': productInfo.trim(),
      '總數量': totalQuantity,
      '訂單金額': `$${order.total_amount}`,
      '訂單狀態': statusMap[order.status] || order.status,
      '付款狀態': paymentStatusMap[order.payment_status] || order.payment_status,
      '備註': order.notes || ''
    });
  });
  
  return excelData;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

// 格式化時間
const formatTime = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

// 匯出訂單到 Excel
const exportOrdersToExcel = async (orderIds = null, filters = {}) => {
  try {
    // 建立查詢條件
    const where = {};
    
    // 如果指定訂單 ID
    if (orderIds && Array.isArray(orderIds) && orderIds.length > 0) {
      where.id = orderIds;
    }
    
    // 應用其他篩選條件
    if (filters.status) {
      where.status = filters.status;
    }
    
    if (filters.startDate || filters.endDate) {
      where.created_at = {};
      if (filters.startDate) {
        where.created_at[Op.gte] = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.created_at[Op.lte] = new Date(filters.endDate);
      }
    }
    
    // 查詢訂單
    const orders = await Order.findAll({
      where,
      order: [['created_at', 'DESC']]
    });
    
    if (orders.length === 0) {
      throw new Error('沒有找到符合條件的訂單');
    }
    
    // 格式化資料
    const excelData = formatOrdersForExcel(orders);
    
    // 創建工作簿
    const wb = XLSX.utils.book_new();
    
    // 創建工作表
    const ws = XLSX.utils.json_to_sheet(excelData, {
      header: [
        '訂單編號',
        '訂單日期',
        '訂單時間',
        '客戶姓名',
        '客戶電話',
        '客戶信箱',
        '7-11門市代號',
        '7-11門市名稱',
        '商品明細',
        '總數量',
        '訂單金額',
        '訂單狀態',
        '付款狀態',
        '備註'
      ]
    });
    
    // 設定欄位寬度
    const colWidths = [
      { wch: 20 }, // 訂單編號
      { wch: 12 }, // 訂單日期
      { wch: 10 }, // 訂單時間
      { wch: 15 }, // 客戶姓名
      { wch: 15 }, // 客戶電話
      { wch: 25 }, // 客戶信箱
      { wch: 15 }, // 7-11門市代號
      { wch: 20 }, // 7-11門市名稱
      { wch: 40 }, // 商品明細
      { wch: 10 }, // 總數量
      { wch: 12 }, // 訂單金額
      { wch: 12 }, // 訂單狀態
      { wch: 12 }, // 付款狀態
      { wch: 30 }  // 備註
    ];
    ws['!cols'] = colWidths;
    
    // 將工作表加入工作簿
    XLSX.utils.book_append_sheet(wb, ws, '訂單資料');
    
    // 生成 Excel 檔案 buffer
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
    
    return {
      buffer,
      filename: generateFileName(),
      count: orders.length
    };
    
  } catch (error) {
    console.error('匯出 Excel 錯誤:', error);
    throw error;
  }
};

// 匯出訂單統計報表
const exportOrderStatistics = async (startDate, endDate) => {
  try {
    // 查詢日期範圍內的訂單
    const where = {
      created_at: {
        [Op.gte]: new Date(startDate),
        [Op.lte]: new Date(endDate)
      }
    };
    
    const orders = await Order.findAll({ where });
    
    // 統計資料
    const stats = {
      totalOrders: orders.length,
      totalRevenue: 0,
      statusCounts: {},
      dailyStats: {}
    };
    
    orders.forEach(order => {
      // 計算總營收
      if (order.payment_status === 'paid') {
        stats.totalRevenue += parseFloat(order.total_amount);
      }
      
      // 統計狀態
      stats.statusCounts[order.status] = (stats.statusCounts[order.status] || 0) + 1;
      
      // 每日統計
      const dateKey = formatDate(order.created_at);
      if (!stats.dailyStats[dateKey]) {
        stats.dailyStats[dateKey] = {
          orders: 0,
          revenue: 0
        };
      }
      stats.dailyStats[dateKey].orders++;
      if (order.payment_status === 'paid') {
        stats.dailyStats[dateKey].revenue += parseFloat(order.total_amount);
      }
    });
    
    // 轉換為 Excel 格式
    const summaryData = [{
      '統計期間': `${formatDate(startDate)} ~ ${formatDate(endDate)}`,
      '總訂單數': stats.totalOrders,
      '總營收': `$${stats.totalRevenue}`,
      '平均訂單金額': stats.totalOrders > 0 ? `$${(stats.totalRevenue / stats.totalOrders).toFixed(2)}` : '$0'
    }];
    
    const dailyData = Object.entries(stats.dailyStats).map(([date, data]) => ({
      '日期': date,
      '訂單數': data.orders,
      '營收': `$${data.revenue}`
    }));
    
    // 創建工作簿
    const wb = XLSX.utils.book_new();
    
    // 創建總覽工作表
    const wsSummary = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, wsSummary, '總覽');
    
    // 創建每日統計工作表
    const wsDaily = XLSX.utils.json_to_sheet(dailyData);
    XLSX.utils.book_append_sheet(wb, wsDaily, '每日統計');
    
    // 生成 Excel 檔案 buffer
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
    
    return {
      buffer,
      filename: `統計報表_${generateFileName()}`,
      stats
    };
    
  } catch (error) {
    console.error('匯出統計報表錯誤:', error);
    throw error;
  }
};

module.exports = {
  exportOrdersToExcel,
  exportOrderStatistics,
  generateFileName
}; 