<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理後台 - 海水不可斗量</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --sidebar-width: 260px;
            --header-height: 70px;
            --primary: #2C3E50;
            --secondary: #34495E;
            --accent: #E67E22;
            --success: #27AE60;
            --danger: #E74C3C;
            --warning: #F39C12;
            --light: #ECF0F1;
            --dark: #1A1A1A;
            --white: #FFFFFF;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background: #F5F6FA;
            color: var(--dark);
            overflow-x: hidden;
        }

        /* 頂部導航欄 */
        .header {
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: var(--header-height);
            background: var(--white);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            z-index: 100;
            transition: left 0.3s ease;
        }

        .header.sidebar-collapsed {
            left: 60px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .menu-toggle {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--secondary);
            cursor: pointer;
            padding: 0.5rem;
            transition: color 0.3s;
        }

        .menu-toggle:hover {
            color: var(--accent);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 1rem;
            background: var(--light);
            border-radius: 8px;
        }

        .user-name {
            font-weight: 500;
        }

        .btn-logout {
            background: var(--danger);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-logout:hover {
            background: #C0392B;
            transform: translateY(-1px);
        }

        /* 側邊欄 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--primary);
            overflow-y: auto;
            transition: width 0.3s ease;
            z-index: 200;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-logo {
            color: var(--white);
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
            display: block;
            transition: opacity 0.3s;
        }

        .sidebar.collapsed .sidebar-logo span {
            display: none;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            position: relative;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: var(--white);
        }

        .menu-item.active {
            background: var(--accent);
            color: var(--white);
        }

        .menu-item i {
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }

        .sidebar.collapsed .menu-item span {
            display: none;
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 1rem;
        }

        /* 主內容區 */
        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            padding: 2rem;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - var(--header-height));
        }

        .main-content.sidebar-collapsed {
            margin-left: 60px;
        }

        /* 統計卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-icon.orders {
            background: rgba(231,76,60,0.1);
            color: var(--danger);
        }

        .stat-icon.revenue {
            background: rgba(46,204,113,0.1);
            color: var(--success);
        }

        .stat-icon.products {
            background: rgba(52,152,219,0.1);
            color: #3498DB;
        }

        .stat-icon.customers {
            background: rgba(155,89,182,0.1);
            color: #9B59B6;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--secondary);
            opacity: 0.8;
        }

        /* 快速操作 */
        .quick-actions {
            background: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            padding: 1.5rem;
            background: var(--light);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: var(--primary);
        }

        .action-btn:hover {
            background: var(--accent);
            color: var(--white);
            transform: translateY(-3px);
        }

        .action-btn i {
            font-size: 2rem;
        }

        .action-btn span {
            font-weight: 500;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }

            .sidebar-logo span,
            .menu-item span {
                display: none;
            }

            .menu-item {
                justify-content: center;
                padding: 1rem;
            }

            .header,
            .main-content {
                left: 60px;
                margin-left: 60px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 載入動畫 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--light);
            border-top-color: var(--accent);
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 載入動畫 -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <!-- 側邊欄 -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="#" class="sidebar-logo">
                <span>管理後台</span>
            </a>
        </div>
        <nav class="sidebar-menu">
            <a href="#dashboard" class="menu-item active" data-page="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>儀表板</span>
            </a>
            <a href="#orders" class="menu-item" data-page="orders">
                <i class="fas fa-shopping-cart"></i>
                <span>訂單管理</span>
            </a>
            <a href="#products" class="menu-item" data-page="products">
                <i class="fas fa-ice-cream"></i>
                <span>產品管理</span>
            </a>
            <a href="#announcements" class="menu-item" data-page="announcements">
                <i class="fas fa-bullhorn"></i>
                <span>公告管理</span>
            </a>
            <a href="#discounts" class="menu-item" data-page="discounts">
                <i class="fas fa-tags"></i>
                <span>優惠管理</span>
            </a>
            <a href="#telegram" class="menu-item" data-page="telegram">
                <i class="fab fa-telegram"></i>
                <span>Telegram 通知</span>
            </a>
            <a href="#settings" class="menu-item" data-page="settings">
                <i class="fas fa-cog"></i>
                <span>系統設定</span>
            </a>
        </nav>
    </aside>

    <!-- 頂部導航 -->
    <header class="header" id="header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="header-title" id="pageTitle">儀表板</h1>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name" id="userName">管理員</span>
            </div>
            <button class="btn-logout" id="logoutBtn">
                <i class="fas fa-sign-out-alt"></i> 登出
            </button>
        </div>
    </header>

    <!-- 主內容區 -->
    <main class="main-content" id="mainContent">
        <!-- 儀表板內容 -->
        <div id="dashboardContent">
            <!-- 統計卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="stat-value" id="totalOrders">0</div>
                    <div class="stat-label">今日訂單</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon revenue">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-value" id="totalRevenue">$0</div>
                    <div class="stat-label">今日營收</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="fas fa-ice-cream"></i>
                    </div>
                    <div class="stat-value" id="totalProducts">0</div>
                    <div class="stat-label">商品總數</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon customers">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value" id="totalCustomers">0</div>
                    <div class="stat-label">本月客戶</div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
                <h2 class="section-title">快速操作</h2>
                <div class="action-grid">
                    <button class="action-btn" data-action="new-order">
                        <i class="fas fa-plus-circle"></i>
                        <span>新增訂單</span>
                    </button>
                    <button class="action-btn" data-action="new-product">
                        <i class="fas fa-ice-cream"></i>
                        <span>新增產品</span>
                    </button>
                    <button class="action-btn" data-action="new-announcement">
                        <i class="fas fa-bullhorn"></i>
                        <span>發布公告</span>
                    </button>
                    <button class="action-btn" data-action="export-orders">
                        <i class="fas fa-file-excel"></i>
                        <span>匯出訂單</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 公告管理 -->
        <div id="announcementsContent" class="page-content" style="display: none;">
             <!-- 公告管理介面會在這裡 -->
        </div>

        <!-- 優惠管理 -->
        <div id="discountsContent" class="page-content" style="display: none;">
            <div class="content-header">
                <h2 class="section-title">優惠管理</h2>
                <button class="btn btn-primary" id="addDiscountBtn">
                    <i class="fas fa-plus"></i> 新增優惠
                </button>
            </div>
            <div class="table-container">
                <table class="data-table" id="discountsTable">
                    <thead>
                        <tr>
                            <th>名稱</th>
                            <th>類型</th>
                            <th>說明</th>
                            <th>狀態</th>
                            <th>開始日期</th>
                            <th>結束日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 優惠資料將動態載入此處 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Telegram 通知設定 -->
        <div id="telegramContent" class="page-content" style="display: none;">
            <h2>Telegram 通知設定 (建置中)</h2>
        </div>

        <!-- 系統設定 -->
        <div id="settingsContent" class="page-content" style="display: none;">
            <h2>系統設定 (建置中)</h2>
        </div>
    </main>
    
    <!-- Modal for Discount Form -->
    <div class="modal" id="discountModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="discountModalTitle">新增優惠</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <form id="discountForm">
                    <input type="hidden" id="discountId" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discountName">優惠名稱</label>
                            <input type="text" id="discountName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="discountType">優惠類型</label>
                            <select id="discountType" name="type" required>
                                <option value="" disabled selected>請選擇類型</option>
                                <option value="percentage">百分比折扣</option>
                                <option value="fixed">固定金額折扣</option>
                                <option value="quantity">數量優惠</option>
                                <option value="bundle">組合優惠</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="discountDescription">優惠說明</label>
                        <textarea id="discountDescription" name="description" rows="3"></textarea>
                    </div>

                    <div id="discountRulesContainer" class="form-group">
                        <!-- 動態規則欄位將顯示於此 -->
                    </div>

                    <div class="form-group">
                        <label for="discountProductIds">適用產品 (可選，留空表示適用所有產品)</label>
                        <select id="discountProductIds" name="product_ids" multiple>
                            <!-- 產品選項將動態載入 -->
                        </select>
                        <small>按住 Ctrl/Cmd 可複選</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="discountStartDate">開始日期 (可選)</label>
                            <input type="datetime-local" id="discountStartDate" name="start_date">
                        </div>
                        <div class="form-group">
                            <label for="discountEndDate">結束日期 (可選)</label>
                            <input type="datetime-local" id="discountEndDate" name="end_date">
                        </div>
                    </div>
                     <div class="form-group inline">
                        <input type="checkbox" id="discountIsActive" name="is_active" checked>
                        <label for="discountIsActive">啟用此優惠</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-action="close">取消</button>
                <button type="submit" form="discountForm" class="btn btn-primary">儲存</button>
            </div>
        </div>
    </div>


    <script>
        const API_URL = 'http://localhost:3000/api';
        let currentPage = 'dashboard';

        // 檢查登入狀態
        const checkAuth = () => {
            const token = localStorage.getItem('adminToken');
            if (!token) {
                window.location.href = 'admin-dashboard.html';
                return false;
            }
            return true;
        };

        // 設定請求標頭
        const getHeaders = () => ({
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        });

        // 載入用戶資訊
        const loadUserInfo = async () => {
            try {
                const response = await fetch(`${API_URL}/auth/admin/me`, {
                    headers: getHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('userName').textContent = data.user.username;
                } else if (response.status === 401) {
                    localStorage.removeItem('adminToken');
                    window.location.href = 'admin-dashboard.html';
                }
            } catch (error) {
                console.error('載入用戶資訊失敗:', error);
            }
        };

        // 載入統計資料
        const loadStats = async () => {
            try {
                // 這裡可以調用實際的 API 來獲取統計資料
                // 暫時使用模擬資料
                document.getElementById('totalOrders').textContent = '12';
                document.getElementById('totalRevenue').textContent = '$2,480';
                document.getElementById('totalProducts').textContent = '24';
                document.getElementById('totalCustomers').textContent = '156';
            } catch (error) {
                console.error('載入統計資料失敗:', error);
            }
        };

        // 側邊欄收合
        document.getElementById('menuToggle').addEventListener('click', () => {
            const sidebar = document.getElementById('sidebar');
            const header = document.getElementById('header');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            header.classList.toggle('sidebar-collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
        });

        // 頁面切換
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 更新活動狀態
                document.querySelectorAll('.menu-item').forEach(m => m.classList.remove('active'));
                item.classList.add('active');

                // 更新頁面標題
                const page = item.dataset.page;
                const title = item.querySelector('span').textContent;
                document.getElementById('pageTitle').textContent = title;

                // 載入對應頁面內容
                loadPageContent(page);
            });
        });

        // 初始化函式：檢查登入並載入用戶與統計，隱藏載入動畫
        const init = async () => {
            if (!checkAuth()) return;
            await loadUserInfo();
            await loadStats();
            setTimeout(() => document.getElementById('loading').style.display = 'none', 500);
        };

        // 載入優惠管理頁面
        async function loadDiscountsPage() {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = document.getElementById('discountsContent').outerHTML;
            await loadDiscounts();
        }
        // 載入公告管理頁面
        async function loadAnnouncementsPage() {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = document.getElementById('announcementsContent').outerHTML;
            // TODO: 載入公告相關邏輯
        }
        // 載入 Telegram 通知設定頁面
        async function loadTelegramPage() {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = document.getElementById('telegramContent').outerHTML;
        }
        // 載入系統設定頁面
        async function loadSettingsPage() {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = document.getElementById('settingsContent').outerHTML;
        }

        // 載入頁面內容
        const loadPageContent = (page) => {
            currentPage = page;
            const mainContent = document.getElementById('mainContent');

            // 根據不同頁面載入不同內容
            switch(page) {
                case 'dashboard':
                    mainContent.innerHTML = document.getElementById('dashboardContent').outerHTML;
                    loadStats();
                    break;
                case 'orders':
                    loadOrdersPage();
                    break;
                case 'products':
                    loadProductsPage();
                    break;
                case 'announcements':
                    loadAnnouncementsPage();
                    break;
                case 'discounts':
                    loadDiscountsPage();
                    break;
                case 'telegram':
                    loadTelegramPage();
                    break;
                case 'settings':
                    loadSettingsPage();
                    break;
            }
        };

        // 快速操作按鈕
        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-btn')) {
                const action = e.target.closest('.action-btn').dataset.action;
                handleQuickAction(action);
            }
        });

        // 處理快速操作
        const handleQuickAction = (action) => {
            switch(action) {
                case 'new-order':
                    alert('新增訂單功能開發中...');
                    break;
                case 'new-product':
                    alert('新增產品功能開發中...');
                    break;
                case 'new-announcement':
                    alert('發布公告功能開發中...');
                    break;
                case 'export-orders':
                    alert('匯出訂單功能開發中...');
                    break;
            }
        };

        // 登出功能
        document.getElementById('logoutBtn').addEventListener('click', () => {
            if (confirm('確定要登出嗎？')) {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUsername');
                window.location.href = 'admin-dashboard.html';
            }
        });

        // 分頁切換函式：更新側邊欄 active 狀態、標題，並載入內容
        function switchPage(page) {
            document.querySelectorAll('.menu-item')
                .forEach(m => m.classList.toggle('active', m.dataset.page === page));
            const titleEl = document.querySelector(`.menu-item[data-page="${page}"] span`);
            if (titleEl) document.getElementById('pageTitle').textContent = titleEl.textContent;
            loadPageContent(page);
        }

        // 單一初始化監聽：驗證、載入、顯示首頁
        document.addEventListener('DOMContentLoaded', async () => {
            if (!checkAuth()) return;
            await init();
            const initialPage = window.location.hash.substring(1) || 'dashboard';
            switchPage(initialPage);
        });

        // =================================================================================
        // 優惠管理 (Discounts Management)
        // =================================================================================
        const discountsTableBody = document.querySelector('#discountsTable tbody');
        const addDiscountBtn = document.getElementById('addDiscountBtn');
        const discountModal = document.getElementById('discountModal');
        const discountForm = document.getElementById('discountForm');
        const discountModalTitle = document.getElementById('discountModalTitle');
        const discountTypeSelect = document.getElementById('discountType');
        const discountRulesContainer = document.getElementById('discountRulesContainer');

        async function loadDiscounts() {
            showLoading();
            try {
                const response = await api.get('/discounts/admin');
                renderDiscounts(response.data);
            } catch (error) {
                console.error('無法載入優惠:', error);
                showToast('無法載入優惠', 'error');
            } finally {
                hideLoading();
            }
        }

        function renderDiscounts(discounts) {
            discountsTableBody.innerHTML = '';
            if (!discounts || discounts.length === 0) {
                discountsTableBody.innerHTML = '<tr><td colspan="7">目前沒有任何優惠</td></tr>';
                return;
            }
            
            discounts.forEach(discount => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${escapeHTML(discount.name)}</td>
                    <td>${getDiscountTypeName(discount.type)}</td>
                    <td>${escapeHTML(discount.description) || '-'}</td>
                    <td>
                        <label class="switch">
                            <input type="checkbox" ${discount.is_active ? 'checked' : ''} onchange="toggleDiscountStatus(${discount.id}, this.checked)">
                            <span class="slider round"></span>
                        </label>
                    </td>
                    <td>${discount.start_date ? new Date(discount.start_date).toLocaleString() : '-'}</td>
                    <td>${discount.end_date ? new Date(discount.end_date).toLocaleString() : '-'}</td>
                    <td>
                        <button class="btn-icon" onclick="editDiscount(${discount.id})"><i class="fas fa-edit"></i></button>
                        <button class="btn-icon danger" onclick="deleteDiscount(${discount.id})"><i class="fas fa-trash"></i></button>
                    </td>
                `;
                discountsTableBody.appendChild(row);
            });
        }
        
        function getDiscountTypeName(type) {
            const types = {
                percentage: '百分比折扣',
                fixed: '固定金額折扣',
                quantity: '數量優惠',
                bundle: '組合優惠'
            };
            return types[type] || type;
        }

        async function toggleDiscountStatus(id, isActive) {
            showLoading();
            try {
                await api.patch(`/discounts/${id}/toggle`);
                showToast(`優惠狀態已更新為 ${isActive ? '啟用' : '停用'}`, 'success');
                loadDiscounts();
                } catch (error) {
                console.error('更新優惠狀態失敗:', error);
                showToast('更新狀態失敗', 'error');
            } finally {
                hideLoading();
            }
        }
        
        function updateDiscountRulesForm(type) {
            discountRulesContainer.innerHTML = '';
            let fieldsHTML = '';
            switch(type) {
                case 'percentage':
                    fieldsHTML = `
                        <label for="rulePercent">折扣百分比</label>
                        <input type="number" id="rulePercent" name="rules.percent" min="1" max="100" required placeholder="例如: 10 表示 9 折">`;
                    break;
                case 'fixed':
                    fieldsHTML = `
                        <label for="ruleAmount">折扣金額</label>
                        <input type="number" id="ruleAmount" name="rules.amount" min="1" required placeholder="例如: 100">`;
                    break;
                case 'quantity':
                    fieldsHTML = `
                        <div class="form-row">
                            <div class="form-group">
                                <label for="ruleMinQuantity">最低數量</label>
                                <input type="number" id="ruleMinQuantity" name="rules.min_quantity" min="2" required placeholder="例如: 3">
                            </div>
                            <div class="form-group">
                                <label for="ruleQuantityPercent">折扣百分比</label>
                                <input type="number" id="ruleQuantityPercent" name="rules.percent" min="1" max="100" required placeholder="例如: 25 表示買三送一">
                            </div>
                        </div>`;
                    break;
                case 'bundle':
                     fieldsHTML = `
                        <label for="ruleBundlePrice">組合價</label>
                        <input type="number" id="ruleBundlePrice" name="rules.price" min="1" required placeholder="例如: 500">
                        <small>請在下方選擇此組合優惠適用的產品 (至少2項)</small>`;
                    break;
            }
            discountRulesContainer.innerHTML = fieldsHTML;
        }

        discountTypeSelect.addEventListener('change', (e) => {
            updateDiscountRulesForm(e.target.value);
        });
        
        async function openDiscountModal(discount = null) {
            discountForm.reset();
            updateDiscountRulesForm('');
            
            // 載入產品列表
            const productSelect = document.getElementById('discountProductIds');
            try {
                const response = await api.get('/products');
                productSelect.innerHTML = response.data.map(p => `<option value="${p.id}">${escapeHTML(p.name)}</option>`).join('');
            } catch (error) {
                console.error('無法載入產品列表', error);
                showToast('無法載入產品列表', 'error');
            }

            if (discount) {
                discountModalTitle.textContent = '編輯優惠';
            document.getElementById('discountId').value = discount.id;
            document.getElementById('discountName').value = discount.name;
                document.getElementById('discountDescription').value = discount.description || '';
            document.getElementById('discountType').value = discount.type;
                document.getElementById('discountIsActive').checked = discount.is_active;
                document.getElementById('discountStartDate').value = discount.start_date ? new Date(discount.start_date).toISOString().slice(0, 16) : '';
                document.getElementById('discountEndDate').value = discount.end_date ? new Date(discount.end_date).toISOString().slice(0, 16) : '';
                
                if (discount.product_ids && discount.product_ids.length > 0) {
                    discount.product_ids.forEach(id => {
                        const option = productSelect.querySelector(`option[value="${id}"]`);
                        if(option) option.selected = true;
                    });
                }
                
                updateDiscountRulesForm(discount.type);
                // 填入規則值
                if(discount.rules) {
                    for (const key in discount.rules) {
                        const input = discountRulesContainer.querySelector(`[name="rules.${key}"]`);
                        if (input) {
                            input.value = discount.rules[key];
                        }
                    }
                }
                
            } else {
                discountModalTitle.textContent = '新增優惠';
            }
            
            discountModal.style.display = 'flex';
        }

        addDiscountBtn.addEventListener('click', () => openDiscountModal());

        discountModal.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') || e.target.classList.contains('close-btn') || e.target.dataset.action === 'close') {
                discountModal.style.display = 'none';
            }
        });

        discountForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            showLoading();

            const formData = new FormData(discountForm);
            const id = formData.get('id');
            const type = formData.get('type');
            
            const data = {
                name: formData.get('name'),
                description: formData.get('description'),
                type: type,
                product_ids: Array.from(document.getElementById('discountProductIds').selectedOptions).map(opt => opt.value),
                start_date: formData.get('start_date') || null,
                end_date: formData.get('end_date') || null,
                is_active: document.getElementById('discountIsActive').checked,
                rules: {}
            };
            
            // 構建 rules 物件
            switch(type) {
                case 'percentage':
                    data.rules.percent = parseFloat(formData.get('rules.percent'));
                    break;
                case 'fixed':
                    data.rules.amount = parseFloat(formData.get('rules.amount'));
                    break;
                case 'quantity':
                    data.rules.min_quantity = parseInt(formData.get('rules.min_quantity'), 10);
                    data.rules.percent = parseFloat(formData.get('rules.percent'));
                    break;
                case 'bundle':
                    data.rules.price = parseFloat(formData.get('rules.price'));
                    break;
            }

            try {
                if (id) {
                    await api.put(`/discounts/${id}`, data);
                    showToast('優惠更新成功', 'success');
                } else {
                    await api.post('/discounts', data);
                    showToast('優惠新增成功', 'success');
                }
                discountModal.style.display = 'none';
                loadDiscounts();
            } catch (error) {
                console.error('儲存優惠失敗:', error);
                const errorMessage = error.response?.data?.message || '操作失敗';
                showToast(errorMessage, 'error');
            } finally {
                hideLoading();
            }
        });

        async function editDiscount(id) {
            showLoading();
            try {
                const response = await api.get(`/discounts/${id}`);
                openDiscountModal(response.data);
            } catch (error) {
                console.error(`無法獲取優惠 ${id}:`, error);
                showToast('無法獲取優惠資訊', 'error');
            } finally {
                hideLoading();
            }
        }

        async function deleteDiscount(id) {
            if (!confirm('確定要刪除這個優惠嗎？此操作無法復原。')) return;

            showLoading();
            try {
                await api.delete(`/discounts/${id}`);
                showToast('優惠已刪除', 'success');
                loadDiscounts();
            } catch (error) {
                console.error('刪除優惠失敗:', error);
                showToast('刪除失敗', 'error');
            } finally {
                hideLoading();
            }
        }
    </script>
    
    <style>
        /* 產品管理頁面樣式 */
        .products-page {
            background: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-actions {
            display: flex;
            gap: 1rem;
        }
        
        .filter-section {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--light);
            border-radius: 8px;
        }
        
        .form-select {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            background: var(--white);
            min-width: 150px;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .product-card {
            background: var(--white);
            border: 1px solid var(--border);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .product-images {
            height: 200px;
            background: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .product-images img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .no-image {
            font-size: 3rem;
            color: var(--gray);
        }
        
        .product-info {
            padding: 1.5rem;
        }
        
        .product-info h4 {
            margin-bottom: 0.5rem;
            color: var(--primary);
        }
        
        .product-meta {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 4px;
        }
        
        .badge-primary {
            background: var(--primary);
            color: var(--white);
        }
        
        .badge-secondary {
            background: var(--secondary);
            color: var(--white);
        }
        
        .badge-info {
            background: #3498DB;
            color: var(--white);
        }
        
        .product-price {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--accent);
            margin-bottom: 0.5rem;
        }
        
        .product-stock {
            font-size: 0.9rem;
            color: var(--gray);
        }
        
        .low-stock {
            color: var(--danger);
            font-weight: 600;
        }
        
        .product-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        /* 按鈕樣式 */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: var(--primary);
            color: var(--white);
        }
        
        .btn-primary:hover {
            background: var(--secondary);
        }
        
        .btn-secondary {
            background: var(--secondary);
            color: var(--white);
        }
        
        .btn-info {
            background: #3498DB;
            color: var(--white);
        }
        
        .btn-success {
            background: var(--success);
            color: var(--white);
        }
        
        .btn-warning {
            background: var(--warning);
            color: var(--white);
        }
        
        .btn-danger {
            background: var(--danger);
            color: var(--white);
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        /* 模態框樣式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .modal-content {
            background: var(--white);
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray);
        }
        
        .close-btn:hover {
            color: var(--dark);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            padding: 0 1.5rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--primary);
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--accent);
        }
        
        .image-preview {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .image-preview img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 6px;
            border: 2px solid var(--border);
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid var(--border);
        }
        
        .empty-message {
            text-align: center;
            color: var(--gray);
            padding: 3rem;
            font-size: 1.1rem;
        }
        
        /* 訂單管理頁面樣式 */
        .orders-page {
            background: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .orders-table-container {
            overflow-x: auto;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--white);
        }
        
        .orders-table th,
        .orders-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        .orders-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--primary);
        }
        
        .orders-table tbody tr:hover {
            background: #F9F9F9;
        }
        
        .order-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .order-number {
            font-family: 'Courier New', monospace;
            font-weight: 600;
        }
        
        .customer-info,
        .store-info {
            line-height: 1.4;
        }
        
        .text-small {
            font-size: 0.875rem;
            color: var(--gray);
        }
        
        .items-cell {
            max-width: 300px;
            font-size: 0.9rem;
        }
        
        .amount-cell {
            font-weight: 600;
            color: var(--accent);
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 20px;
        }
        
        .status-pending {
            background: #FFF3CD;
            color: #856404;
        }
        
        .status-confirmed {
            background: #CCE5FF;
            color: #004085;
        }
        
        .status-preparing {
            background: #E2E3E5;
            color: #383D41;
        }
        
        .status-ready {
            background: #D1ECF1;
            color: #0C5460;
        }
        
        .status-delivered {
            background: #D4EDDA;
            color: #155724;
        }
        
        .status-cancelled {
            background: #F8D7DA;
            color: #721C24;
        }
        
        .actions-cell {
            white-space: nowrap;
        }
        
        .loading-cell,
        .empty-cell {
            text-align: center;
            color: var(--gray);
            padding: 3rem;
        }
        
        /* 訂單詳情樣式 */
        .modal-large {
            max-width: 800px;
        }
        
        .order-detail-content {
            padding: 1.5rem;
        }
        
        .detail-section {
            margin-bottom: 2rem;
        }
        
        .detail-section h4 {
            margin-bottom: 1rem;
            color: var(--primary);
            border-bottom: 1px solid var(--border);
            padding-bottom: 0.5rem;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .detail-item {
            display: flex;
            gap: 0.5rem;
        }
        
        .detail-item label {
            font-weight: 600;
            color: var(--secondary);
        }
        
        .detail-items-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .detail-items-table th,
        .detail-items-table td {
            padding: 0.75rem;
            text-align: left;
            border: 1px solid var(--border);
        }
        
        .detail-items-table th {
            background: var(--light);
            font-weight: 600;
        }
        
        .detail-items-table tfoot td {
            font-weight: 600;
            background: var(--light);
        }
        
        .text-right {
            text-align: right;
        }
        
        /* 邊框樣式 */
        .border {
            border: 1px solid #E0E0E0;
        }
        
        /* 灰色變數 */
        .gray {
            color: #7F8C8D;
        }
        
        /* Telegram 頁面樣式 */
        .telegram-page {
            background: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .telegram-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .config-section,
        .notification-section,
        .actions-section,
        .help-section {
            background: #F8F9FA;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid var(--border);
        }
        
        .config-section h3,
        .notification-section h3,
        .actions-section h3,
        .help-section h3 {
            margin-bottom: 1rem;
            color: var(--primary);
            font-size: 1.1rem;
        }
        
        .config-status {
            padding: 1rem;
            background: var(--white);
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .config-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .form-help {
            display: block;
            font-size: 0.875rem;
            color: var(--gray);
            margin-top: 0.25rem;
        }
        
        .form-help a {
            color: var(--accent);
            text-decoration: none;
        }
        
        .form-help a:hover {
            text-decoration: underline;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        /* 開關樣式 */
        .switch-label {
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            margin-bottom: 1.5rem;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            background: var(--gray);
            border-radius: 24px;
            transition: background 0.3s;
        }
        
        .switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: var(--white);
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        
        input[type="checkbox"]:checked + .switch {
            background: var(--success);
        }
        
        input[type="checkbox"]:checked + .switch::after {
            transform: translateX(26px);
        }
        
        input[type="checkbox"] {
            display: none;
        }
        
        .notification-types h4 {
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
            color: var(--secondary);
        }
        
        .notification-types ul {
            list-style: none;
            padding: 0;
        }
        
        .notification-types li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .text-success {
            color: var(--success);
        }
        
        .text-info {
            color: #3498DB;
        }
        
        .text-danger {
            color: var(--danger);
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .help-section {
            grid-column: 1 / -1;
        }
        
        .help-section ol {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .help-section li {
            margin-bottom: 0.75rem;
            line-height: 1.6;
        }
        
        .help-section code {
            background: var(--white);
            padding: 0.125rem 0.375rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: var(--accent);
        }
        
                 @media (max-width: 768px) {
             .telegram-content {
                 grid-template-columns: 1fr;
             }
         }
         
         /* 公告管理頁面樣式 */
         .announcements-page {
             background: var(--white);
             padding: 2rem;
             border-radius: 10px;
             box-shadow: 0 2px 10px rgba(0,0,0,0.05);
         }
         
         .announcements-list {
             margin-top: 2rem;
         }
         
         .announcement-card {
             background: var(--white);
             border: 1px solid var(--border);
             border-radius: 10px;
             padding: 1.5rem;
             margin-bottom: 1rem;
             transition: all 0.3s;
         }
         
         .announcement-card:hover {
             transform: translateY(-2px);
             box-shadow: 0 4px 12px rgba(0,0,0,0.1);
         }
         
         .announcement-card.inactive {
             opacity: 0.6;
             background: #F8F9FA;
         }
         
         .announcement-header {
             display: flex;
             justify-content: space-between;
             align-items: flex-start;
             margin-bottom: 1rem;
         }
         
         .announcement-title-section {
             flex: 1;
         }
         
         .announcement-type {
             display: inline-block;
             padding: 0.25rem 0.75rem;
             font-size: 0.75rem;
             font-weight: 600;
             border-radius: 20px;
             margin-bottom: 0.5rem;
         }
         
         .type-info {
             background: #E3F2FD;
             color: #1976D2;
         }
         
         .type-warning {
             background: #FFF3CD;
             color: #856404;
         }
         
         .type-success {
             background: #D4EDDA;
             color: #155724;
         }
         
         .type-promotion {
             background: #F8D7DA;
             color: #721C24;
         }
         
         .announcement-title {
             margin: 0.5rem 0;
             color: var(--primary);
             font-size: 1.2rem;
         }
         
         .announcement-actions {
             display: flex;
             align-items: center;
             gap: 0.5rem;
         }
         
         .status-active {
             background: var(--success);
             color: var(--white);
         }
         
         .status-inactive {
             background: var(--gray);
             color: var(--white);
         }
         
         .announcement-content {
             margin-bottom: 1rem;
             line-height: 1.6;
             color: var(--secondary);
         }
         
         .announcement-footer {
             display: flex;
             gap: 1.5rem;
             font-size: 0.875rem;
             color: var(--gray);
             padding-top: 1rem;
             border-top: 1px solid var(--border);
         }
         
         /* 響應式調整 */
         @media (max-width: 768px) {
             .announcement-header {
                 flex-direction: column;
                 gap: 1rem;
             }
             
             .announcement-actions {
                 flex-wrap: wrap;
             }
             
             .announcement-footer {
                 flex-direction: column;
                 gap: 0.5rem;
             }
         }
    </style>
</body>
</html> 