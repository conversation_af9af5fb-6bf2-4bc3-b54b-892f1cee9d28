/* 🍦 海水不可斗量 - 手機版優化樣式 */

/* 手機版特定變數 */
@media (max-width: 768px) {
    :root {
        --mobile-header-height: 56px;
        --mobile-bottom-bar: 70px;
        --mobile-safe-top: env(safe-area-inset-top);
        --mobile-safe-bottom: env(safe-area-inset-bottom);
    }
}

/* 防止橫向滾動 */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
        width: 100%;
    }
}

/* 優化觸控體驗 */
@media (max-width: 768px) {
    /* 增大所有可點擊元素的觸控區域 */
    button, a, .clickable {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* 移除 hover 效果，改用 active */
    *:hover {
        transform: none !important;
    }
    
    *:active {
        opacity: 0.8;
        transition: opacity 0.1s;
    }
}

/* 優化導航欄 */
@media (max-width: 768px) {
    .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding-top: var(--mobile-safe-top);
        box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    }
    
    .nav-container {
        padding: 12px 16px;
        height: var(--mobile-header-height);
    }
    
    .logo {
        font-size: 16px;
        line-height: 1.2;
    }
    
    .logo-subtitle {
        font-size: 8px;
        opacity: 0.7;
    }
    
    .cart-button {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .cart-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        min-width: 20px;
        height: 20px;
        font-size: 11px;
        border-radius: 10px;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
}

/* 優化主要內容區域 */
@media (max-width: 768px) {
    .main-container {
        padding-top: calc(var(--mobile-header-height) + var(--mobile-safe-top));
        padding-bottom: calc(var(--mobile-bottom-bar) + var(--mobile-safe-bottom) + 20px);
    }
    
    .hero {
        margin-top: 0;
        padding: 24px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0 0 20px 20px;
        margin-bottom: 24px;
    }
    
    .hero h1 {
        font-size: 24px;
        margin-bottom: 8px;
        font-weight: 700;
    }
    
    .hero p {
        font-size: 14px;
        opacity: 0.95;
    }
}

/* 優化區塊標題 */
@media (max-width: 768px) {
    .section {
        margin-bottom: 32px;
    }
    
    .section-header {
        padding: 0 16px;
        margin-bottom: 16px;
    }
    
    .section-title {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 4px;
    }
    
    .section-subtitle {
        font-size: 11px;
        letter-spacing: 1.5px;
        opacity: 0.6;
    }
}

/* 優化產品卡片 - 採用橫向滾動 */
@media (max-width: 768px) {
    .product-grid {
        display: flex;
        gap: 12px;
        padding: 0 16px;
        overflow-x: auto;
        overflow-y: hidden;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .product-grid::-webkit-scrollbar {
        display: none;
    }
    
    .product-card {
        flex: 0 0 160px;
        scroll-snap-align: start;
        border-radius: 16px;
        padding: 16px;
        background: white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .product-card:active {
        transform: scale(0.98);
    }
    
    .product-card.selected {
        border: 2px solid var(--accent);
        box-shadow: 0 6px 20px rgba(230, 126, 34, 0.3);
    }
    
    .product-card.selected::after {
        content: '✓';
        position: absolute;
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        background: var(--accent);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
    }
    
    .product-name {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1.3;
    }
    
    .product-description {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.4;
    }
    
    .product-price {
        font-size: 18px;
        font-weight: 700;
        color: var(--accent);
    }
}

/* 優化配料分類標籤 */
@media (max-width: 768px) {
    .category-filters {
        display: flex;
        gap: 8px;
        padding: 0 16px 16px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
    }
    
    .category-filters::-webkit-scrollbar {
        display: none;
    }
    
    .category-btn {
        flex-shrink: 0;
        padding: 8px 20px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 20px;
        background: white;
        border: 1px solid #E0E0E0;
        transition: all 0.2s;
    }
    
    .category-btn.active {
        background: var(--accent);
        color: white;
        border-color: var(--accent);
        box-shadow: 0 2px 8px rgba(230, 126, 34, 0.3);
    }
}

/* 優化底部固定欄 */
@media (max-width: 768px) {
    .add-to-cart-section {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 12px 16px;
        padding-bottom: calc(12px + var(--mobile-safe-bottom));
        box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        z-index: 900;
        transform: translateY(100%);
        transition: transform 0.3s ease;
    }
    
    .add-to-cart-section.show {
        transform: translateY(0);
    }
    
    .add-to-cart-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
    }
    
    .selection-summary {
        flex: 1;
    }
    
    .selection-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
    }
    
    .selection-details {
        display: flex;
        align-items: baseline;
        gap: 8px;
    }
    
    .selection-details span:first-child {
        font-size: 15px;
        font-weight: 600;
        color: #333;
    }
    
    .selection-price {
        font-size: 18px;
        font-weight: 700;
        color: var(--accent);
    }
    
    .main-add-to-cart-btn {
        background: var(--accent);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
        transition: all 0.2s;
    }
    
    .main-add-to-cart-btn:active:not(:disabled) {
        transform: scale(0.95);
    }
    
    .main-add-to-cart-btn:disabled {
        background: #CCC;
        box-shadow: none;
    }
}

/* 優化購物車側邊欄 */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100%;
        right: -100%;
        top: 0;
        bottom: 0;
        transition: right 0.3s ease;
        box-shadow: -4px 0 20px rgba(0,0,0,0.2);
    }
    
    .cart-sidebar.open {
        right: 0;
    }
    
    .cart-header {
        padding: 16px;
        padding-top: calc(16px + var(--mobile-safe-top));
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .cart-title {
        font-size: 20px;
        font-weight: 700;
    }
    
    .cart-close {
        width: 40px;
        height: 40px;
        border: none;
        background: #F5F5F5;
        border-radius: 50%;
        font-size: 20px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .cart-content {
        padding: 16px;
        height: calc(100vh - var(--mobile-header-height) - 160px);
        overflow-y: auto;
    }
    
    .cart-item {
        background: #F8F9FA;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
    }
    
    .cart-item-header {
        margin-bottom: 8px;
    }
    
    .cart-item-name {
        font-size: 16px;
        font-weight: 600;
    }
    
    .cart-item-price {
        font-size: 16px;
        font-weight: 700;
        color: var(--accent);
    }
    
    .cart-item-details {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
        line-height: 1.5;
    }
    
    .quantity-control {
        background: white;
        border-radius: 25px;
        padding: 4px;
        display: inline-flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .quantity-control button {
        width: 32px;
        height: 32px;
        border: none;
        background: none;
        border-radius: 50%;
        font-size: 16px;
        color: var(--primary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .quantity-control button:active {
        background: #F5F5F5;
    }
    
    .remove-btn {
        width: 36px;
        height: 36px;
        border: none;
        background: #FFEBEE;
        color: #F44336;
        border-radius: 50%;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .cart-footer {
        padding: 16px;
        padding-bottom: calc(16px + var(--mobile-safe-bottom));
        background: white;
        box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
    }
    
    .cart-total {
        margin-bottom: 16px;
    }
    
    .cart-total-label {
        font-size: 14px;
        color: #666;
    }
    
    .cart-total-amount {
        font-size: 24px;
        font-weight: 700;
    }
    
    .checkout-btn {
        width: 100%;
        padding: 16px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
    }
}

/* 優化訂單表單 */
@media (max-width: 768px) {
    .order-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        z-index: 2000;
        transform: translateY(100%);
        transition: transform 0.3s ease;
    }
    
    .order-modal.show {
        transform: translateY(0);
    }
    
    .order-form {
        width: 100%;
        height: 100%;
        background: white;
        display: flex;
        flex-direction: column;
    }
    
    .form-header {
        padding: 16px;
        padding-top: calc(16px + var(--mobile-safe-top));
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .form-title {
        font-size: 20px;
        font-weight: 700;
        margin-left: 12px;
    }
    
    .back-btn {
        width: 40px;
        height: 40px;
        border: none;
        background: #F5F5F5;
        border-radius: 50%;
        font-size: 20px;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    form {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        padding-bottom: 100px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        padding: 14px 16px;
        font-size: 16px;
        border: 1px solid #E0E0E0;
        border-radius: 12px;
        background: #F8F9FA;
        transition: all 0.2s;
        -webkit-appearance: none;
        appearance: none;
    }
    
    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--accent);
        background: white;
        box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
    }
    
    .form-textarea {
        min-height: 100px;
        resize: vertical;
    }
    
    /* 7-11 門市選擇按鈕 */
    .btn.btn-primary[onclick*="openSevenElevenMap"] {
        width: 100%;
        padding: 14px 16px;
        background: #F8F9FA;
        border: 1px solid #E0E0E0;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn.btn-primary[onclick*="openSevenElevenMap"]:active {
        background: #E0E0E0;
    }
    
    .btn.btn-primary[onclick*="openSevenElevenMap"] i {
        color: var(--accent);
    }
    
    #selectedStoreInfo {
        background: #E8F4F8;
        border-radius: 12px;
        padding: 12px 16px;
        margin-top: 8px;
        font-size: 14px;
        line-height: 1.5;
    }
    
    .form-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 16px;
        padding-bottom: calc(16px + var(--mobile-safe-bottom));
        box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        display: flex;
        gap: 12px;
    }
    
    .form-btn {
        flex: 1;
        padding: 16px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .form-btn-cancel {
        background: #F5F5F5;
        color: #666;
    }
    
    .form-btn-submit {
        background: var(--accent);
        color: white;
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
    }
    
    .form-btn:active {
        transform: scale(0.98);
    }
}

/* 優化成功訊息 */
@media (max-width: 768px) {
    .success-overlay {
        padding: 24px;
    }
    
    .success-box {
        width: 100%;
        max-width: 320px;
        padding: 32px 24px;
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    }
    
    .success-icon {
        font-size: 60px;
        margin-bottom: 20px;
    }
    
    .success-title {
        font-size: 24px;
        margin-bottom: 12px;
    }
    
    .success-message {
        font-size: 16px;
        margin-bottom: 16px;
        line-height: 1.5;
    }
    
    .success-order-number {
        font-size: 14px;
        padding: 8px 16px;
        background: #F5F5F5;
        border-radius: 8px;
        font-family: monospace;
    }
}

/* 優化公告顯示 */
@media (max-width: 768px) {
    .announcements-container {
        padding: 0 16px;
        margin-bottom: 24px;
    }
    
    .announcement {
        padding: 12px 16px;
        margin-bottom: 12px;
        border-radius: 12px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        position: relative;
    }
    
    .announcement-icon {
        font-size: 20px;
        flex-shrink: 0;
        margin-top: 2px;
    }
    
    .announcement-body {
        flex: 1;
        padding-right: 24px;
    }
    
    .announcement-title {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1.3;
    }
    
    .announcement-text {
        font-size: 14px;
        line-height: 1.5;
        color: #666;
    }
    
    .announcement-close {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 28px;
        height: 28px;
        border: none;
        background: rgba(0,0,0,0.05);
        border-radius: 50%;
        font-size: 16px;
        color: #999;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .announcement-close:active {
        background: rgba(0,0,0,0.1);
    }
}

/* 優化浮動結帳按鈕 */
@media (max-width: 768px) {
    .floating-checkout {
        bottom: calc(16px + var(--mobile-safe-bottom));
        left: 50%;
        transform: translateX(-50%);
        padding: 12px 24px;
        border-radius: 30px;
        background: rgba(26, 26, 26, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 15px;
        font-weight: 600;
        transition: all 0.3s;
        z-index: 800;
    }
    
    .floating-checkout.show {
        display: flex;
    }
    
    .floating-checkout:active {
        transform: translateX(-50%) scale(0.95);
    }
    
    .floating-checkout-badge {
        background: var(--accent);
        color: white;
        min-width: 20px;
        height: 20px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6px;
    }
    
    .floating-checkout-price {
        font-size: 16px;
        font-weight: 700;
    }
}

/* Toast 訊息優化 */
@media (max-width: 768px) {
    .toast-success {
        bottom: calc(100px + var(--mobile-safe-bottom));
        left: 16px;
        right: 16px;
        transform: translateY(100px);
        padding: 16px 20px;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 500;
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .toast-success.show {
        transform: translateY(0);
    }
    
    .toast-success i {
        font-size: 20px;
    }
}

/* 載入動畫優化 */
@media (max-width: 768px) {
    .loading {
        padding: 40px;
    }
    
    .loading-spinner {
        width: 36px;
        height: 36px;
        border-width: 3px;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    body {
        background: #1A1A1A;
        color: #E0E0E0;
    }
    
    .navbar,
    .cart-sidebar,
    .order-modal,
    .product-card,
    .cart-item,
    .form-input,
    .form-select,
    .form-textarea {
        background: #2A2A2A;
        color: #E0E0E0;
        border-color: #444;
    }
    
    .hero {
        background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
    }
}

/* 平板優化 (768px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        padding: 0 24px;
    }
    
    .main-container {
        max-width: 900px;
        margin: 0 auto;
    }
    
    .cart-sidebar {
        width: 400px;
    }
}

/* 橫向模式優化 */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 16px;
    }
    
    .hero h1 {
        font-size: 20px;
    }
    
    .section {
        margin-bottom: 20px;
    }
    
    .product-card {
        padding: 12px;
    }
    
    .cart-content {
        height: calc(100vh - 120px);
    }
} 