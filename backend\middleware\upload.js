const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

// 確保上傳目錄存在
const ensureUploadDir = async () => {
  const uploadDir = path.join(__dirname, '..', 'uploads', 'products');
  try {
    await fs.access(uploadDir);
  } catch {
    await fs.mkdir(uploadDir, { recursive: true });
  }
  return uploadDir;
};

// Multer 儲存設定
const storage = multer.memoryStorage();

// 檔案過濾器
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('只允許上傳 JPG、PNG、WebP 格式的圖片'), false);
  }
};

// Multer 設定
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 5 // 最多5張圖片
  }
});

// 處理圖片並儲存
const processAndSaveImage = async (file, productId) => {
  await ensureUploadDir();
  
  const timestamp = Date.now();
  const filename = `product-${productId}-${timestamp}.webp`;
  const filepath = path.join(__dirname, '..', 'uploads', 'products', filename);
  const thumbnailPath = path.join(__dirname, '..', 'uploads', 'products', `thumb-${filename}`);
  
  try {
    // 處理主圖片（調整大小，轉換為 WebP）
    await sharp(file.buffer)
      .resize(800, 800, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 85 })
      .toFile(filepath);
    
    // 生成縮圖
    await sharp(file.buffer)
      .resize(300, 300, {
        fit: 'cover',
        position: 'center'
      })
      .webp({ quality: 80 })
      .toFile(thumbnailPath);
    
    return {
      url: `/uploads/products/${filename}`,
      thumbnail: `/uploads/products/thumb-${filename}`,
      size: file.size,
      originalName: file.originalname
    };
  } catch (error) {
    console.error('圖片處理錯誤:', error);
    throw new Error('圖片處理失敗');
  }
};

// 刪除舊圖片
const deleteProductImages = async (images) => {
  if (!images || !Array.isArray(images)) return;
  
  for (const image of images) {
    try {
      if (image.url) {
        const filepath = path.join(__dirname, '..', image.url);
        await fs.unlink(filepath).catch(() => {});
      }
      if (image.thumbnail) {
        const thumbpath = path.join(__dirname, '..', image.thumbnail);
        await fs.unlink(thumbpath).catch(() => {});
      }
    } catch (error) {
      console.error('刪除圖片錯誤:', error);
    }
  }
};

module.exports = {
  upload,
  processAndSaveImage,
  deleteProductImages
}; 