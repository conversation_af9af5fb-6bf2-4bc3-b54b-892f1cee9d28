# Telegram Bot 通知設定指南

## 功能說明

系統支援透過 Telegram Bot 即時發送以下通知：
- 🎉 新訂單通知
- 🔄 訂單狀態更新
- 💳 付款狀態更新
- 📊 每日營業報告

## 設定步驟

### 1. 創建 Telegram Bot

1. 在 Telegram 搜尋 **@BotFather**
2. 發送 `/newbot` 命令
3. 輸入 Bot 名稱（例如：海水冰淇淋店）
4. 輸入 Bot 使用者名稱（必須以 bot 結尾，例如：seawater_icecream_bot）
5. 複製 Bot Token（格式如：`1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`）

### 2. 取得 Chat ID

方法一：使用 @userinfobot
1. 在 Telegram 搜尋 **@userinfobot**
2. 發送任意訊息
3. 機器人會回覆您的 Chat ID

方法二：使用您的 Bot
1. 先發送訊息給您剛創建的 Bot
2. 訪問：`https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. 在返回的 JSON 中找到 `chat.id`

### 3. 在系統中設定

1. 登入管理後台
2. 點擊側邊欄「Telegram 通知」
3. 填入 Bot Token 和 Chat ID
4. 點擊「測試連接」確認設定正確
5. 開啟「啟用通知」開關

## 通知範例

### 新訂單通知
```
🎉 新訂單通知

📋 訂單編號：ORD202410201530123
👤 客戶姓名：王小明
📱 客戶電話：0912345678
🏪 取貨門市：北車門市

🍦 訂購商品：
1. 經典香草 x2 + 比利時巧克力醬, 野生藍莓 - NT$ 230

💰 總金額：NT$ 230
📝 備註：少冰，謝謝！

⏰ 訂單時間：2024/10/20 15:30:23
```

### 訂單狀態更新
```
🔄 訂單狀態更新

📋 訂單編號：ORD202410201530123
👤 客戶：王小明

📊 狀態變更：
待確認 ➡️ 製作中
```

## 常見問題

### Q: 無法收到通知？
A: 請檢查：
1. Bot Token 和 Chat ID 是否正確
2. 是否已開啟「啟用通知」
3. 是否有先發送訊息給 Bot（Bot 無法主動發送訊息給未互動過的用戶）

### Q: 如何關閉特定類型的通知？
A: 目前系統會發送所有類型的通知，未來版本將支援自訂通知類型。

### Q: 可以設定多個接收者嗎？
A: 目前僅支援單一 Chat ID，若需要多人接收，可以：
1. 創建 Telegram 群組
2. 將 Bot 加入群組
3. 使用群組的 Chat ID

## 安全提醒

⚠️ **重要**：Bot Token 是敏感資訊，請勿外洩！
- 不要將 Token 提交到版本控制系統
- 不要在公開場合分享 Token
- 如果 Token 外洩，請立即到 @BotFather 重新生成

## 技術支援

如有問題，請聯繫系統管理員。 