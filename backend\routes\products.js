const express = require('express');
const router = express.Router();
const { Product, Discount } = require('../models');
const { adminAuthMiddleware } = require('./auth');
const { upload, processAndSaveImage, deleteProductImages } = require('../middleware/upload');
const { Op } = require('sequelize');

// 獲取所有產品（公開）
router.get('/', async (req, res) => {
  try {
    const { type, category, available } = req.query;
    const where = {};
    
    if (type) where.type = type;
    if (category) where.category = category;
    if (available !== undefined) where.available = available === 'true';
    
    const products = await Product.findAll({
      where,
      order: [['type', 'ASC'], ['category', 'ASC'], ['name', 'ASC']]
    });
    
    res.json(products);
  } catch (error) {
    console.error('獲取產品錯誤:', error);
    res.status(500).json({ message: '獲取產品失敗' });
  }
});

// 獲取單一產品（公開）
router.get('/:id', async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '產品不存在' });
    }
    res.json(product);
  } catch (error) {
    res.status(500).json({ message: '獲取產品失敗' });
  }
});

// 新增產品（需要管理員權限）
router.post('/', adminAuthMiddleware, upload.array('images', 5), async (req, res) => {
  try {
    const productData = req.body;
    
    // 創建產品
    const product = await Product.create(productData);
    
    // 處理圖片上傳
    if (req.files && req.files.length > 0) {
      const images = [];
      for (const file of req.files) {
        const imageData = await processAndSaveImage(file, product.id);
        images.push(imageData);
      }
      
      // 更新產品圖片
      await product.update({ images });
    }
    
    res.status(201).json(product);
  } catch (error) {
    console.error('新增產品錯誤:', error);
    res.status(400).json({ message: error.message });
  }
});

// 更新產品（需要管理員權限）
router.put('/:id', adminAuthMiddleware, upload.array('images', 5), async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '產品不存在' });
    }
    
    const updateData = req.body;
    
    // 處理新圖片上傳
    if (req.files && req.files.length > 0) {
      // 刪除舊圖片
      if (product.images && product.images.length > 0) {
        await deleteProductImages(product.images);
      }
      
      // 上傳新圖片
      const images = [];
      for (const file of req.files) {
        const imageData = await processAndSaveImage(file, product.id);
        images.push(imageData);
      }
      
      updateData.images = images;
    }
    
    await product.update(updateData);
    res.json(product);
  } catch (error) {
    console.error('更新產品錯誤:', error);
    res.status(400).json({ message: error.message });
  }
});

// 刪除產品（需要管理員權限）
router.delete('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '產品不存在' });
    }
    
    // 刪除相關圖片
    if (product.images && product.images.length > 0) {
      await deleteProductImages(product.images);
    }
    
    await product.destroy();
    res.json({ message: '產品已刪除' });
  } catch (error) {
    console.error('刪除產品錯誤:', error);
    res.status(500).json({ message: '刪除產品失敗' });
  }
});

// 更新庫存（需要管理員權限）
router.patch('/:id/stock', adminAuthMiddleware, async (req, res) => {
  try {
    const { stock } = req.body;
    
    if (stock === undefined || stock < 0) {
      return res.status(400).json({ message: '庫存數量無效' });
    }
    
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '產品不存在' });
    }
    
    await product.update({ stock });
    res.json({ message: '庫存更新成功', stock: product.stock });
  } catch (error) {
    res.status(500).json({ message: '更新庫存失敗' });
  }
});

// 批量更新庫存（需要管理員權限）
router.patch('/batch-update-stock', adminAuthMiddleware, async (req, res) => {
  try {
    const { updates } = req.body; // [{id, stock}, ...]
    
    if (!Array.isArray(updates)) {
      return res.status(400).json({ message: '請提供更新陣列' });
    }
    
    const results = [];
    for (const update of updates) {
      const product = await Product.findByPk(update.id);
      if (product && update.stock >= 0) {
        await product.update({ stock: update.stock });
        results.push({ id: update.id, success: true });
      } else {
        results.push({ id: update.id, success: false });
      }
    }
    
    res.json({ message: '批量更新完成', results });
  } catch (error) {
    res.status(500).json({ message: '批量更新失敗' });
  }
});

// 獲取低庫存產品（需要管理員權限）
router.get('/admin/low-stock', adminAuthMiddleware, async (req, res) => {
  try {
    const threshold = parseInt(req.query.threshold) || 10;
    
    const products = await Product.findAll({
      where: {
        stock: {
          [Op.lte]: threshold
        }
      },
      order: [['stock', 'ASC'], ['name', 'ASC']]
    });
    
    res.json(products);
  } catch (error) {
    res.status(500).json({ message: '獲取低庫存產品失敗' });
  }
});

// 初始化範例資料（需要管理員權限）
router.post('/init-samples', adminAuthMiddleware, async (req, res) => {
  try {
    // 檢查是否已有資料
    const existingCount = await Product.count();
    if (existingCount > 0) {
      return res.status(400).json({ message: '已存在產品資料，請先清空' });
    }

    // 10種基底口味
    const bases = [
      { name: '香草', type: 'base', price: 80, description: '經典香草口味', stock: 100 },
      { name: '巧克力', type: 'base', price: 85, description: '濃郁巧克力', stock: 100 },
      { name: '草莓', type: 'base', price: 85, description: '新鮮草莓製成', stock: 100 },
      { name: '抹茶', type: 'base', price: 90, description: '日式抹茶風味', stock: 100 },
      { name: '芒果', type: 'base', price: 90, description: '熱帶芒果口味', stock: 100 },
      { name: '薄荷巧克力', type: 'base', price: 95, description: '清新薄荷配巧克力碎片', stock: 100 },
      { name: '焦糖海鹽', type: 'base', price: 95, description: '焦糖與海鹽的完美結合', stock: 100 },
      { name: '榴槤', type: 'base', price: 100, description: '濃郁榴槤果肉', stock: 50 },
      { name: '芋頭', type: 'base', price: 85, description: '台灣芋頭香甜口感', stock: 100 },
      { name: '檸檬雪酪', type: 'base', price: 80, description: '清爽檸檬雪酪', stock: 100 }
    ];

    // 30種配料（分6類）
    const toppings = [
      // 糖漿類 (5種)
      { name: '巧克力醬', type: 'topping', category: '糖漿', price: 15, stock: 999 },
      { name: '焦糖醬', type: 'topping', category: '糖漿', price: 15, stock: 999 },
      { name: '草莓醬', type: 'topping', category: '糖漿', price: 15, stock: 999 },
      { name: '藍莓醬', type: 'topping', category: '糖漿', price: 20, stock: 999 },
      { name: '蜂蜜', type: 'topping', category: '糖漿', price: 20, stock: 999 },
      
      // 水果類 (5種)
      { name: '新鮮草莓', type: 'topping', category: '水果', price: 25, stock: 50 },
      { name: '藍莓', type: 'topping', category: '水果', price: 30, stock: 50 },
      { name: '香蕉片', type: 'topping', category: '水果', price: 20, stock: 100 },
      { name: '芒果丁', type: 'topping', category: '水果', price: 25, stock: 80 },
      { name: '奇異果片', type: 'topping', category: '水果', price: 25, stock: 60 },
      
      // 堅果類 (5種)
      { name: '杏仁片', type: 'topping', category: '堅果', price: 20, stock: 200 },
      { name: '核桃', type: 'topping', category: '堅果', price: 25, stock: 150 },
      { name: '花生碎', type: 'topping', category: '堅果', price: 15, stock: 300 },
      { name: '腰果', type: 'topping', category: '堅果', price: 30, stock: 100 },
      { name: '開心果', type: 'topping', category: '堅果', price: 35, stock: 80 },
      
      // 巧克力類 (5種)
      { name: '巧克力豆', type: 'topping', category: '巧克力', price: 20, stock: 200 },
      { name: 'Oreo碎片', type: 'topping', category: '巧克力', price: 20, stock: 250 },
      { name: '巧克力碎片', type: 'topping', category: '巧克力', price: 25, stock: 200 },
      { name: '白巧克力片', type: 'topping', category: '巧克力', price: 25, stock: 150 },
      { name: '黑巧克力醬', type: 'topping', category: '巧克力', price: 20, stock: 999 },
      
      // 糖果類 (5種)
      { name: '彩虹糖', type: 'topping', category: '糖果', price: 15, stock: 300 },
      { name: '軟糖熊', type: 'topping', category: '糖果', price: 20, stock: 250 },
      { name: '棉花糖', type: 'topping', category: '糖果', price: 15, stock: 200 },
      { name: '跳跳糖', type: 'topping', category: '糖果', price: 20, stock: 150 },
      { name: '焦糖爆米花', type: 'topping', category: '糖果', price: 25, stock: 180 },
      
      // 特色配料 (5種)
      { name: '珍珠', type: 'topping', category: '特色', price: 20, stock: 300 },
      { name: '布朗尼塊', type: 'topping', category: '特色', price: 30, stock: 100 },
      { name: '起司蛋糕塊', type: 'topping', category: '特色', price: 35, stock: 80 },
      { name: '抹茶粉', type: 'topping', category: '特色', price: 15, stock: 999 },
      { name: '餅乾碎', type: 'topping', category: '特色', price: 15, stock: 300 }
    ];

    await Product.bulkCreate([...bases, ...toppings]);
    
    res.json({ 
      message: '範例資料已建立', 
      basesCount: bases.length, 
      toppingsCount: toppings.length,
      total: bases.length + toppings.length
    });
  } catch (error) {
    console.error('初始化範例資料錯誤:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router; 