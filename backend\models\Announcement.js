const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Announcement = sequelize.define('announcement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('info', 'warning', 'success', 'promotion'),
    defaultValue: 'info'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '優先級，數字越大越優先顯示'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '開始顯示日期'
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '結束顯示日期'
  }
});

module.exports = Announcement; 