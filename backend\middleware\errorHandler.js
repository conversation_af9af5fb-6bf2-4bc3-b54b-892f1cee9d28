const { ValidationError, UniqueConstraintError, ForeignKeyConstraintError } = require('sequelize');

// 全域錯誤處理中間件
const errorHandler = (err, req, res, next) => {
  console.error('錯誤詳情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    timestamp: new Date().toISOString()
  });

  // Sequelize 驗證錯誤
  if (err instanceof ValidationError) {
    const errors = err.errors.map(error => ({
      field: error.path,
      message: error.message,
      value: error.value
    }));
    
    return res.status(400).json({
      message: '資料驗證失敗',
      errors: errors
    });
  }

  // Sequelize 唯一性約束錯誤
  if (err instanceof UniqueConstraintError) {
    const field = err.errors[0]?.path || 'unknown';
    return res.status(409).json({
      message: `${field} 已存在`,
      field: field
    });
  }

  // Sequelize 外鍵約束錯誤
  if (err instanceof ForeignKeyConstraintError) {
    return res.status(400).json({
      message: '關聯資料不存在或無效'
    });
  }

  // JWT 錯誤
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      message: '無效的認證令牌'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      message: '認證令牌已過期'
    });
  }

  // 檔案上傳錯誤
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      message: '檔案大小超過限制'
    });
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    return res.status(413).json({
      message: '檔案數量超過限制'
    });
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      message: '不支援的檔案類型'
    });
  }

  // 語法錯誤
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json({
      message: 'JSON 格式錯誤'
    });
  }

  // 自定義業務邏輯錯誤
  if (err.name === 'BusinessError') {
    return res.status(err.statusCode || 400).json({
      message: err.message
    });
  }

  // 資料庫連接錯誤
  if (err.name === 'SequelizeConnectionError') {
    return res.status(503).json({
      message: '資料庫連接失敗，請稍後再試'
    });
  }

  // 預設伺服器錯誤
  res.status(500).json({
    message: process.env.NODE_ENV === 'production' 
      ? '伺服器內部錯誤' 
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
};

// 404 錯誤處理
const notFoundHandler = (req, res) => {
  res.status(404).json({
    message: `找不到路由: ${req.method} ${req.url}`
  });
};

// 自定義業務邏輯錯誤類
class BusinessError extends Error {
  constructor(message, statusCode = 400) {
    super(message);
    this.name = 'BusinessError';
    this.statusCode = statusCode;
  }
}

// 非同步錯誤包裝器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 請求驗證中間件
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        message: '請求資料驗證失敗',
        errors: errors
      });
    }
    next();
  };
};

// 速率限制錯誤處理
const rateLimitHandler = (req, res) => {
  res.status(429).json({
    message: '請求過於頻繁，請稍後再試',
    retryAfter: Math.round(req.rateLimit.resetTime / 1000)
  });
};

module.exports = {
  errorHandler,
  notFoundHandler,
  BusinessError,
  asyncHandler,
  validateRequest,
  rateLimitHandler
};
