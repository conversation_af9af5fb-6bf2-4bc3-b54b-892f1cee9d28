const express = require('express');
const router = express.Router();
const cors = require('cors');
const sevenEleven = require('../services/sevenEleven');

// 儲存選擇的門市資訊（實際應用中應該存在 session 或資料庫）
let selectedStoreData = {};

// 啟用 CORS for 7-11 callback
router.use(cors());

// 7-11 門市選擇回調
router.post('/callback', express.urlencoded({ extended: true }), async (req, res) => {
    try {
        console.log('收到 7-11 門市回調資料:', req.body);
        
        // 7-11 API 通常會回傳這些欄位
        const {
            CVSStoreID,      // 門市代號
            CVSStoreName,    // 門市名稱
            CVSAddress,      // 門市地址
            CVSTelephone,    // 門市電話
            CVSOutSide,      // 是否為外縣市
            ExtraData        // 額外資料
        } = req.body;
        
        // 儲存門市資訊
        selectedStoreData = {
            storeId: CVSStoreID,
            storeName: CVSStoreName,
            address: CVSAddress,
            telephone: CVSTelephone,
            isOutside: CVSOutSide === '1',
            extraData: ExtraData,
            timestamp: new Date()
        };
        
        // 回傳成功頁面，並自動關閉視窗
        res.send(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>門市選擇成功</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        margin: 0;
                        background-color: #f5f5f5;
                    }
                    .success-box {
                        background: white;
                        padding: 2rem;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        text-align: center;
                    }
                    .store-info {
                        margin: 1rem 0;
                        text-align: left;
                        background: #f8f8f8;
                        padding: 1rem;
                        border-radius: 5px;
                    }
                    .success-icon {
                        color: #4CAF50;
                        font-size: 3rem;
                        margin-bottom: 1rem;
                    }
                    button {
                        background: #2196F3;
                        color: white;
                        border: none;
                        padding: 0.5rem 2rem;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 1rem;
                        margin-top: 1rem;
                    }
                    button:hover {
                        background: #1976D2;
                    }
                </style>
            </head>
            <body>
                <div class="success-box">
                    <div class="success-icon">✓</div>
                    <h2>門市選擇成功！</h2>
                    <div class="store-info">
                        <p><strong>門市名稱：</strong>${CVSStoreName || '未知'}</p>
                        <p><strong>門市代號：</strong>${CVSStoreID || '未知'}</p>
                        <p><strong>門市地址：</strong>${CVSAddress || '未知'}</p>
                        <p><strong>聯絡電話：</strong>${CVSTelephone || '未知'}</p>
                    </div>
                    <p>請返回原視窗繼續操作</p>
                    <button onclick="window.close()">關閉視窗</button>
                </div>
                <script>
                    // 通知父視窗
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'storeSelected',
                            store: {
                                CVSStoreID: '${CVSStoreID || ''}',
                                CVSStoreName: '${CVSStoreName || ''}',
                                CVSAddress: '${CVSAddress || ''}',
                                CVSTelephone: '${CVSTelephone || ''}'
                            }
                        }, '*');
                    }
                    
                    // 5秒後自動關閉
                    setTimeout(() => {
                        window.close();
                    }, 5000);
                </script>
            </body>
            </html>
        `);
    } catch (error) {
        console.error('處理 7-11 回調錯誤:', error);
        res.status(500).send('處理門市選擇時發生錯誤');
    }
});

// 取得最後選擇的門市資訊
router.get('/selected-store', (req, res) => {
    if (selectedStoreData.storeId) {
        res.json({
            success: true,
            data: selectedStoreData
        });
    } else {
        res.json({
            success: false,
            message: '尚未選擇門市'
        });
    }
});

// 開啟 7-11 門市選擇頁面的 API
router.get('/select-store', (req, res) => {
    const { returnUrl } = req.query;
    const callbackUrl = returnUrl || `http://localhost:3000/api/seven-eleven/callback`;
    
    // 建構 7-11 門市選擇 URL
    const mapUrl = `https://emap.presco.com.tw/c2cemap.ashx?eshopid=870&servicetype=1&url=${encodeURIComponent(callbackUrl)}`;
    
    res.json({
        success: true,
        mapUrl: mapUrl,
        message: '請在新視窗中選擇門市'
    });
});

// 獲取 7-11 門市列表（公開）
router.get('/stores', async (req, res) => {
  try {
    const { city, keyword } = req.query;
    
    let stores = [];
    
    if (city) {
      stores = sevenEleven.getStoresByCity(city);
    } else if (keyword) {
      stores = sevenEleven.searchStores(keyword);
    } else {
      stores = sevenEleven.getAllStores();
    }
    
    res.json(stores);
  } catch (error) {
    console.error('獲取門市列表錯誤:', error);
    res.status(500).json({ message: '獲取門市列表失敗' });
  }
});

// 獲取 7-11 城市列表（公開）
router.get('/cities', async (req, res) => {
  try {
    const cities = sevenEleven.getCities();
    res.json(cities);
  } catch (error) {
    console.error('獲取城市列表錯誤:', error);
    res.status(500).json({ message: '獲取城市列表失敗' });
  }
});

module.exports = router; 