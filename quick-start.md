# Quick Start

This guide helps you set up and run the Ice Cream Shop project locally.

## Prerequisites

-   Node.js (v18.x or later recommended)
-   npm (usually comes with Node.js)

## Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-repo/ice-cream-shop.git
    cd ice-cream-shop
    ```

2.  **Install backend dependencies:**
    ```bash
    cd backend
    npm install
    ```
    This will install Express.js, Sequelize, SQLite3, and other necessary packages. The SQLite database file will be automatically created in the `backend/database` directory on first run.

## Running the Application

1.  **Navigate to the project root directory.**

2.  **Run the start script:**
    ```bash
    start.bat
    ```
    Or on macOS/Linux:
    ```bash
    ./start.sh
    ```
    
    The script provides options to start the backend server only or the full system with a live server for the frontend.
    
    -   **Backend**: Runs on `http://localhost:3000`
    -   **Frontend**: Opens `index.html` on a live server, typically `http://localhost:8080`

## Default Admin Credentials

-   **Username**: `admin`
-   **Password**: `admin123`

# 🚀 海水不可斗量 - 快速啟動指南

## 方案 1：純前端展示（1分鐘）

### 步驟：
1. 直接雙擊開啟 `customer-app.html` 
2. 直接雙擊開啟 `admin-dashboard.html`

✅ 完成！可以立即查看介面效果

---

## 方案 2：使用 Live Server（2分鐘）

### 步驟：
1. 安裝 VS Code
2. 安裝 Live Server 擴充套件
3. 右鍵點擊 HTML 檔案 → "Open with Live Server"

✅ 優點：支援自動重新整理

---

## 方案 3：本地完整系統（10分鐘）

### 前置需求：
- Node.js (v14+)
- MongoDB

### 步驟：

```bash
# 1. 進入後端目錄
cd backend

# 2. 安裝依賴
npm install

# 3. 啟動 MongoDB
mongod

# 4. 啟動後端服務
npm start
```

### 測試：
- 後端 API：http://localhost:5000
- 前端頁面：開啟 HTML 檔案

---

## 方案 4：Vercel 快速部署（5分鐘）

### 步驟：

1. 註冊 Vercel 帳號
2. 安裝 Vercel CLI
```bash
npm i -g vercel
```

3. 部署
```bash
vercel
```

4. 選擇設定：
   - Project Name: ice-cream-shop
   - Directory: ./
   - Build Command: (留空)

✅ 獲得線上網址！

---

## 方案 5：GitHub Pages（3分鐘）

### 步驟：

1. 上傳到 GitHub
2. Settings → Pages
3. Source: Deploy from a branch
4. Branch: main / root

✅ 網址：https://[你的用戶名].github.io/ice-cream-shop/

---

## 🎯 推薦方案

### 展示用途：
- **最快**：方案 1（直接開啟 HTML）
- **最專業**：方案 4（Vercel 部署）

### 開發測試：
- **最完整**：方案 3（本地完整系統）
- **最方便**：方案 2（Live Server）

---

## 📱 手機測試

### 本地測試：
1. 確保電腦和手機在同一網路
2. 查詢電腦 IP：
```bash
ipconfig  # Windows
ifconfig  # Mac/Linux
```
3. 手機瀏覽器輸入：http://[電腦IP]:5500/customer-app.html

### 線上測試：
- 使用 Vercel 或 GitHub Pages 部署後直接訪問

---

## ⚡ 超快速啟動腳本

創建 `start.bat` (Windows) 或 `start.sh` (Mac/Linux)：

### Windows (start.bat)：
```batch
@echo off
echo 啟動冰淇淋商店系統...
cd backend
start npm start
timeout /t 3
start http://localhost:5000
start ../customer-app.html
start ../admin-dashboard.html
```

### Mac/Linux (start.sh)：
```bash
#!/bin/bash
echo "啟動冰淇淋商店系統..."
cd backend
npm start &
sleep 3
open http://localhost:5000
open ../customer-app.html
open ../admin-dashboard.html
```

執行後自動開啟所有頁面！ 