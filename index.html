<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海水不可斗量 - 義式手工冰淇淋</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background: #1A1A1A;
            color: #FFFFFF;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            text-align: center;
            max-width: 600px;
        }

        .logo {
            margin-bottom: 3rem;
        }

        .logo h1 {
            font-size: 3rem;
            font-weight: 700;
            letter-spacing: -1px;
            margin-bottom: 0.5rem;
        }

        .logo p {
            font-size: 1.2rem;
            font-weight: 300;
            letter-spacing: 2px;
            opacity: 0.8;
        }

        .nav-grid {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 3rem;
        }

        .nav-card {
            background: #2C3E50;
            padding: 2.5rem;
            text-decoration: none;
            color: #FFFFFF;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #E67E22;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .nav-card:hover {
            background: #34495E;
            transform: translateY(-5px);
        }

        .nav-card:hover::before {
            transform: translateX(0);
        }

        .nav-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }

        .nav-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .nav-desc {
            font-size: 0.9rem;
            opacity: 0.7;
            line-height: 1.6;
        }

        .footer {
            margin-top: 4rem;
            font-size: 0.9rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .logo h1 {
                font-size: 2rem;
            }

            .logo p {
                font-size: 1rem;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-card {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo" id="logoSection" style="cursor: pointer;">
            <h1>海水不可斗量</h1>
            <p>THE SEA IS IMMEASURABLE</p>
        </div>

        <div class="nav-grid">
            <a href="customer-app.html" class="nav-card">
                <div class="nav-icon">🍨</div>
                <h2 class="nav-title">線上訂購</h2>
                <p class="nav-desc">探索10種基底口味與30種精選配料，打造專屬於您的義式手工冰淇淋</p>
            </a>


        </div>

        <div class="footer">
            <p>© 2024 海水不可斗量 - 義式手工冰淇淋專賣店</p>
        </div>
    </div>

    <script>
        // 隱藏管理員入口 - 點擊Logo 5次
        let clickCount = 0;
        let clickTimer = null;
        
        document.getElementById('logoSection').addEventListener('click', function() {
            clickCount++;
            
            // 重置計時器
            if (clickTimer) {
                clearTimeout(clickTimer);
            }
            
            // 5秒內需要點擊5次
            clickTimer = setTimeout(() => {
                clickCount = 0;
            }, 5000);
            
            // 達到5次點擊時跳轉到管理員登入頁面
            if (clickCount === 5) {
                clickCount = 0;
                window.location.href = 'admin-dashboard.html';
            }
        });
    </script>
</body>
</html>