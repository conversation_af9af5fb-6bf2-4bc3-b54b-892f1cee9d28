const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const { testConnection } = require('./config/database');
const { syncDatabase } = require('./models');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 初始化資料庫
const initDatabase = async () => {
  await testConnection();
  await syncDatabase();
};

// Routes
app.use('/api/products', require('./routes/products'));
app.use('/api/orders', require('./routes/orders'));
app.use('/api/auth', require('./routes/auth'));
app.use('/api/discounts', require('./routes/discounts'));
app.use('/api/telegram', require('./routes/telegram'));
app.use('/api/announcements', require('./routes/announcements'));
app.use('/api/seven-eleven', require('./routes/sevenEleven'));

app.get('/', (req, res) => {
  res.json({ 
    message: 'Ice Cream Shop API is running!',
    database: 'SQLite',
    version: '1.0.0'
  });
});

// 啟動伺服器
const startServer = async () => {
  try {
    await initDatabase();
app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
    });
  } catch (error) {
    console.error('❌ 無法啟動伺服器:', error);
    process.exit(1);
  }
};

startServer(); 