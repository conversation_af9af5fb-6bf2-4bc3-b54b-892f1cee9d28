const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const { User, Admin } = require('../models');

// JWT Secret (實際應用中應該放在環境變數)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';

// 生成 JWT Token
const generateToken = (user, type = 'user') => {
  const payload = type === 'admin' 
    ? { 
        id: user.id, 
        username: user.username,
        type: 'admin'
      }
    : { 
        id: user.id, 
      username: user.username,
      email: user.email,
        role: user.role,
        type: 'user'
      };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
};

// 註冊新用戶（僅供初始設置使用）
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, role } = req.body;
    
    // 檢查用戶是否已存在
    const existingUser = await User.findOne({ 
      where: {
        [Op.or]: [{ email }, { username }]
      }
    });
    if (existingUser) {
      return res.status(400).json({ message: '用戶名或電子郵件已存在' });
    }
    
    const user = await User.create({
      username,
      email,
      password,
      role: role || 'staff'
    });
    
    const token = generateToken(user);
    
    res.status(201).json({
      message: '註冊成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 登入
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 查找用戶（可用 username 或 email 登入）
    const user = await User.findOne({
      where: {
        [Op.or]: [{ username }, { email: username }]
      }
    });
    
    if (!user || !user.is_active) {
      return res.status(401).json({ message: '用戶名或密碼錯誤' });
    }
    
    // 驗證密碼
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ message: '用戶名或密碼錯誤' });
    }
    
    const token = generateToken(user);
    
    res.json({
      message: '登入成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 驗證 Token 中間件
const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      throw new Error();
    }
    
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password'] }
    });
    
    if (!user || !user.is_active) {
      throw new Error();
    }
    
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: '請先登入' });
  }
};

// 獲取當前用戶資訊
router.get('/me', authMiddleware, (req, res) => {
  res.json({
    user: {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      role: req.user.role
    }
  });
});

// 管理員登入
router.post('/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 查找管理員
    const admin = await Admin.findOne({ where: { username } });
    
    if (!admin || !admin.is_active) {
      return res.status(401).json({ message: '帳號或密碼錯誤' });
    }
    
    // 驗證密碼
    const isMatch = await admin.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ message: '帳號或密碼錯誤' });
    }
    
    // 更新最後登入時間
    await admin.update({ last_login: new Date() });
    
    const token = generateToken(admin, 'admin');
    
    res.json({
      message: '登入成功',
      user: {
        id: admin.id,
        username: admin.username,
        type: 'admin'
      },
      token
    });
  } catch (error) {
    console.error('管理員登入錯誤:', error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
});

// 管理員驗證中間件
const adminAuthMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      throw new Error();
    }
    
    const decoded = jwt.verify(token, JWT_SECRET);
    
    if (decoded.type !== 'admin') {
      throw new Error();
    }
    
    const admin = await Admin.findByPk(decoded.id);
    
    if (!admin || !admin.is_active) {
      throw new Error();
    }
    
    req.admin = admin;
    next();
  } catch (error) {
    res.status(401).json({ message: '請先登入管理員帳號' });
  }
};

// 獲取當前管理員資訊
router.get('/admin/me', adminAuthMiddleware, (req, res) => {
  res.json({
    user: {
      id: req.admin.id,
      username: req.admin.username,
      last_login: req.admin.last_login,
      type: 'admin'
    }
  });
});

// 快速驗證管理員 Token
router.get('/admin/verify', adminAuthMiddleware, (req, res) => {
  res.status(200).json({
    message: 'Token is valid',
    user: {
      id: req.admin.id,
      username: req.admin.username,
      last_login: req.admin.last_login,
      type: 'admin'
    }
  });
});

// 修改管理員密碼
router.post('/admin/change-password', adminAuthMiddleware, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({ message: '新密碼至少需要6個字元' });
    }
    
    // 驗證當前密碼
    const isMatch = await req.admin.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(401).json({ message: '當前密碼錯誤' });
    }
    
    // 更新密碼
    req.admin.password = newPassword;
    await req.admin.save();
    
    res.json({ message: '密碼修改成功' });
  } catch (error) {
    res.status(500).json({ message: '修改密碼失敗' });
  }
});

module.exports = router;
module.exports.authMiddleware = authMiddleware; 
module.exports.adminAuthMiddleware = adminAuthMiddleware; 