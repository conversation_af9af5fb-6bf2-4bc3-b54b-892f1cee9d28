<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#E67E22">
    <title>結帳 - 海水不可斗量</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #E67E22;
            --primary-dark: #D35400;
            --secondary: #34495E;
            --success: #27AE60;
            --danger: #E74C3C;
            --warning: #F39C12;
            --info: #3498DB;
            --light: #ECF0F1;
            --dark: #2C3E50;
            --white: #FFFFFF;
            --border: #BDC3C7;
            --text: #2C3E50;
            --text-light: #7F8C8D;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: var(--text);
            line-height: 1.6;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            padding-top: calc(1rem + env(safe-area-inset-top));
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .back-btn {
            background: var(--secondary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
        }

        .checkout-form {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            font-size: 16px; /* 防止 iOS 自動縮放 */
            transition: border-color 0.3s ease;
            -webkit-appearance: none;
            appearance: none;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-input.error {
            border-color: var(--danger);
        }

        .error-message {
            color: var(--danger);
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .store-selection {
            border: 2px solid var(--border);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--light);
        }

        .store-selection:hover {
            border-color: var(--primary);
        }

        .store-selection.selected {
            border-color: var(--primary);
            background: rgba(230, 126, 34, 0.05);
        }

        .store-info {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: var(--white);
            border-radius: 8px;
            border: 1px solid var(--border);
        }

        .store-info.show {
            display: block;
        }

        .order-summary {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 2rem;
        }

        .cart-item {
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .item-toppings {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .item-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-quantity {
            color: var(--text-light);
        }

        .item-price {
            font-weight: 600;
            color: var(--primary);
        }

        .price-breakdown {
            border-top: 2px solid var(--border);
            padding-top: 1rem;
            margin-top: 1rem;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .price-row.total {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary);
            border-top: 1px solid var(--border);
            padding-top: 0.5rem;
            margin-top: 1rem;
        }

        .submit-btn {
            width: 100%;
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            background: var(--primary-dark);
        }

        .submit-btn:disabled {
            background: var(--border);
            cursor: not-allowed;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--light);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 1rem;
                gap: 1rem;
            }

            .header-content {
                padding: 0 1rem;
            }

            .order-summary {
                position: static;
                order: -1; /* 在手機版中將訂單摘要移到上方 */
            }

            .checkout-form {
                padding: 1.5rem;
            }

            .form-input,
            .submit-btn {
                min-height: 44px; /* 觸控友好的最小高度 */
                -webkit-tap-highlight-color: transparent;
            }

            .store-selection {
                min-height: 44px;
                display: flex;
                align-items: center;
            }

            .submit-btn {
                padding-bottom: calc(1rem + env(safe-area-inset-bottom));
            }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 載入動畫 -->
    <div class="loading hidden" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <!-- 頂部導航 -->
    <header class="header">
        <div class="header-content">
            <a href="customer-app.html" class="logo">
                <i class="fas fa-ice-cream"></i>
                海水不可斗量
            </a>
            <a href="customer-app.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                返回選購
            </a>
        </div>
    </header>

    <!-- 主要內容 -->
    <main class="main-content">
        <!-- 結帳表單 -->
        <div class="checkout-form">
            <h2 class="section-title">
                <i class="fas fa-user section-icon"></i>
                訂購資訊
            </h2>
            
            <form id="checkoutForm">
                <div class="form-group">
                    <label class="form-label" for="customerName">姓名 *</label>
                    <input type="text" id="customerName" name="customerName" class="form-input" required>
                    <div class="error-message" id="nameError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="customerEmail">電子郵件 *</label>
                    <input type="email" id="customerEmail" name="customerEmail" class="form-input" required>
                    <div class="error-message" id="emailError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="customerPhone">手機號碼 *</label>
                    <input type="tel" id="customerPhone" name="customerPhone" class="form-input" required>
                    <div class="error-message" id="phoneError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">7-11 取貨門市 *</label>
                    <div class="store-selection" id="storeSelection">
                        <i class="fas fa-store"></i>
                        點擊選擇 7-11 門市
                    </div>
                    <div class="store-info" id="storeInfo">
                        <!-- 選中的門市資訊將顯示在這裡 -->
                    </div>
                    <div class="error-message" id="storeError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="notes">備註 (可選)</label>
                    <textarea id="notes" name="notes" class="form-input" rows="3" placeholder="有任何特殊需求請在此說明..."></textarea>
                </div>
            </form>
        </div>

        <!-- 訂單摘要 -->
        <div class="order-summary">
            <h2 class="section-title">
                <i class="fas fa-receipt section-icon"></i>
                訂單摘要
            </h2>
            
            <div id="cartItems">
                <!-- 購物車項目將通過 JavaScript 動態載入 -->
            </div>
            
            <div class="price-breakdown">
                <div class="price-row">
                    <span>小計：</span>
                    <span id="subtotal">$0</span>
                </div>
                <div class="price-row">
                    <span>運費：</span>
                    <span>免費</span>
                </div>
                <div class="price-row total">
                    <span>總計：</span>
                    <span id="total">$0</span>
                </div>
            </div>
            
            <button type="submit" class="submit-btn" id="submitBtn" form="checkoutForm">
                <i class="fas fa-credit-card"></i>
                確認訂購
            </button>
        </div>
    </main>

    <script>
        // API 基礎 URL
        const API_URL = 'http://localhost:3001/api';
        
        // 全域變數
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        let selectedStore = null;
        
        // DOM 元素
        const loading = document.getElementById('loading');
        const cartItems = document.getElementById('cartItems');
        const subtotal = document.getElementById('subtotal');
        const total = document.getElementById('total');
        const storeSelection = document.getElementById('storeSelection');
        const storeInfo = document.getElementById('storeInfo');
        const checkoutForm = document.getElementById('checkoutForm');
        const submitBtn = document.getElementById('submitBtn');
        
        // 渲染購物車項目
        function renderCartItems() {
            const itemsHtml = cart.map(item => `
                <div class="cart-item">
                    <div class="item-name">${item.baseName}</div>
                    <div class="item-toppings">
                        ${item.toppings.length > 0 ?
                          '配料: ' + item.toppings.map(t => t.name).join(', ') :
                          '無配料'}
                    </div>
                    <div class="item-details">
                        <span class="item-quantity">數量: ${item.quantity}</span>
                        <span class="item-price">$${item.totalPrice}</span>
                    </div>
                </div>
            `).join('');

            cartItems.innerHTML = itemsHtml;

            const totalAmount = cart.reduce((sum, item) => sum + item.totalPrice, 0);
            subtotal.textContent = `$${totalAmount}`;
            total.textContent = `$${totalAmount}`;
        }

        // 設置事件監聽器
        function setupEventListeners() {
            // 門市選擇
            storeSelection.addEventListener('click', openStoreSelector);

            // 表單提交
            checkoutForm.addEventListener('submit', handleSubmit);

            // 監聽門市選擇回調
            window.addEventListener('message', handleStoreSelection);
        }

        // 開啟門市選擇器
        function openStoreSelector() {
            const callbackUrl = encodeURIComponent(`http://localhost:3001/api/seven-eleven/callback`);
            const mapUrl = `https://emap.presco.com.tw/c2cemap.ashx?eshopid=870&servicetype=1&url=${callbackUrl}`;

            const popup = window.open(
                mapUrl,
                'storeSelector',
                'width=800,height=600,scrollbars=yes,resizable=yes'
            );

            if (!popup) {
                alert('請允許彈出視窗以選擇門市');
            }
        }

        // 處理門市選擇回調
        function handleStoreSelection(event) {
            if (event.origin !== window.location.origin) return;

            if (event.data && event.data.type === 'storeSelected') {
                selectedStore = event.data.store;
                updateStoreDisplay();
            }
        }

        // 更新門市顯示
        function updateStoreDisplay() {
            if (!selectedStore) return;

            storeSelection.classList.add('selected');
            storeSelection.innerHTML = `
                <i class="fas fa-store"></i>
                ${selectedStore.CVSStoreName}
            `;

            storeInfo.innerHTML = `
                <div><strong>門市名稱：</strong>${selectedStore.CVSStoreName}</div>
                <div><strong>門市代號：</strong>${selectedStore.CVSStoreID}</div>
                <div><strong>門市地址：</strong>${selectedStore.CVSAddress}</div>
                <div><strong>門市電話：</strong>${selectedStore.CVSTelephone}</div>
            `;
            storeInfo.classList.add('show');

            clearError('storeError');
        }

        // 表單驗證
        function validateForm() {
            let isValid = true;

            // 清除之前的錯誤
            clearAllErrors();

            // 驗證姓名
            const name = document.getElementById('customerName').value.trim();
            if (!name) {
                showError('nameError', '請輸入姓名');
                isValid = false;
            }

            // 驗證電子郵件
            const email = document.getElementById('customerEmail').value.trim();
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email) {
                showError('emailError', '請輸入電子郵件');
                isValid = false;
            } else if (!emailRegex.test(email)) {
                showError('emailError', '請輸入有效的電子郵件格式');
                isValid = false;
            }

            // 驗證手機號碼
            const phone = document.getElementById('customerPhone').value.trim();
            const phoneRegex = /^09\d{8}$/;
            if (!phone) {
                showError('phoneError', '請輸入手機號碼');
                isValid = false;
            } else if (!phoneRegex.test(phone)) {
                showError('phoneError', '請輸入有效的手機號碼格式 (09xxxxxxxx)');
                isValid = false;
            }

            // 驗證門市選擇
            if (!selectedStore) {
                showError('storeError', '請選擇 7-11 取貨門市');
                isValid = false;
            }

            return isValid;
        }

        // 顯示錯誤訊息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            const inputElement = errorElement.previousElementSibling;

            errorElement.textContent = message;
            if (inputElement && inputElement.classList.contains('form-input')) {
                inputElement.classList.add('error');
            }
        }

        // 清除錯誤訊息
        function clearError(elementId) {
            const errorElement = document.getElementById(elementId);
            const inputElement = errorElement.previousElementSibling;

            errorElement.textContent = '';
            if (inputElement && inputElement.classList.contains('form-input')) {
                inputElement.classList.remove('error');
            }
        }

        // 清除所有錯誤訊息
        function clearAllErrors() {
            ['nameError', 'emailError', 'phoneError', 'storeError'].forEach(clearError);
        }

        // 處理表單提交
        async function handleSubmit(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            // 顯示載入動畫
            loading.classList.remove('hidden');
            submitBtn.disabled = true;

            try {
                // 準備訂單資料
                const orderData = {
                    customer_name: document.getElementById('customerName').value.trim(),
                    customer_email: document.getElementById('customerEmail').value.trim(),
                    customer_phone: document.getElementById('customerPhone').value.trim(),
                    seven_store_id: selectedStore.CVSStoreID,
                    notes: document.getElementById('notes').value.trim(),
                    items: cart.map(item => ({
                        baseId: item.baseId,
                        toppingIds: item.toppings.map(t => t.id),
                        quantity: item.quantity
                    }))
                };

                // 發送訂單
                const response = await fetch(`${API_URL}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (response.ok) {
                    // 訂單成功
                    localStorage.removeItem('cart');
                    showSuccessMessage('訂單提交成功！', result);
                } else {
                    throw new Error(result.message || '訂單提交失敗');
                }

            } catch (error) {
                console.error('提交訂單錯誤:', error);
                alert('訂單提交失敗：' + error.message);
            } finally {
                loading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        }

        // 顯示成功訊息
        function showSuccessMessage(message, orderData) {
            const successHtml = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                ">
                    <div style="
                        background: white;
                        padding: 2rem;
                        border-radius: 12px;
                        text-align: center;
                        max-width: 500px;
                        margin: 1rem;
                    ">
                        <div style="color: var(--success); font-size: 3rem; margin-bottom: 1rem;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2 style="color: var(--text); margin-bottom: 1rem;">訂單提交成功！</h2>
                        <p style="color: var(--text-light); margin-bottom: 1rem;">
                            您的訂單編號：<strong style="color: var(--primary);">${orderData.order_number}</strong>
                        </p>
                        <p style="color: var(--text-light); margin-bottom: 2rem;">
                            我們已收到您的訂單，將盡快為您準備。<br>
                            您將在 ${selectedStore.CVSStoreName} 取貨。
                        </p>
                        <button onclick="window.location.href='customer-app.html'" style="
                            background: var(--primary);
                            color: white;
                            border: none;
                            padding: 1rem 2rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                        ">
                            繼續購物
                        </button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', successHtml);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            if (cart.length === 0) {
                alert('購物車是空的，請先選購商品');
                window.location.href = 'customer-app.html';
                return;
            }

            renderCartItems();
            setupEventListeners();
        });
    </script>
</body>
</html>
