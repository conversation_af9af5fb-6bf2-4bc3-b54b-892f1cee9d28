const express = require('express');
const router = express.Router();
const { adminAuthMiddleware } = require('./auth');
const telegramBot = require('../services/telegramBot');

// 獲取 Telegram Bot 配置（需要管理員權限）
router.get('/config', adminAuthMiddleware, async (req, res) => {
  try {
    const config = await telegramBot.getConfig();
    res.json(config);
  } catch (error) {
    console.error('獲取 Telegram 配置錯誤:', error);
    res.status(500).json({ message: '獲取配置失敗' });
  }
});

// 更新 Telegram Bot 配置（需要管理員權限）
router.post('/config', adminAuthMiddleware, async (req, res) => {
  try {
    const { token, chatId } = req.body;
    
    if (!token || !chatId) {
      return res.status(400).json({ message: '請提供 Token 和 Chat ID' });
    }
    
    const result = await telegramBot.saveConfig(token, chatId);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('更新 Telegram 配置錯誤:', error);
    res.status(500).json({ message: '更新配置失敗' });
  }
});

// 測試 Telegram Bot 連接（需要管理員權限）
router.post('/test', adminAuthMiddleware, async (req, res) => {
  try {
    const result = await telegramBot.testConnection();
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('測試 Telegram 連接錯誤:', error);
    res.status(500).json({ message: '測試失敗' });
  }
});

// 切換 Telegram Bot 啟用狀態（需要管理員權限）
router.post('/toggle', adminAuthMiddleware, async (req, res) => {
  try {
    const result = await telegramBot.toggleEnabled();
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('切換 Telegram 狀態錯誤:', error);
    res.status(500).json({ message: '切換狀態失敗' });
  }
});

// 發送每日報告（需要管理員權限）
router.post('/daily-report', adminAuthMiddleware, async (req, res) => {
  try {
    // 獲取今日統計
    const { Order } = require('../models');
    const { Op } = require('sequelize');
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // 今日訂單數
    const todayOrders = await Order.count({
      where: {
        created_at: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        }
      }
    });
    
    // 今日營收
    const todayRevenueResult = await Order.sum('total_amount', {
      where: {
        created_at: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        },
        payment_status: 'paid'
      }
    });
    
    // 待處理訂單
    const pendingOrders = await Order.count({
      where: { status: 'pending' }
    });
    
    // 本月訂單數
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthOrders = await Order.count({
      where: {
        created_at: {
          [Op.gte]: monthStart
        }
      }
    });
    
    const stats = {
      todayOrders,
      todayRevenue: todayRevenueResult || 0,
      pendingOrders,
      monthOrders
    };
    
    await telegramBot.sendDailyReport(stats);
    
    res.json({ message: '每日報告已發送' });
  } catch (error) {
    console.error('發送每日報告錯誤:', error);
    res.status(500).json({ message: '發送報告失敗' });
  }
});

module.exports = router; 