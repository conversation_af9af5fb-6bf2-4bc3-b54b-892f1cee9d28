<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理員登入 - 海水不可斗量</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background: #1A1A1A;
            color: #FFFFFF;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            background: #2C3E50;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.9;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            background: #34495E;
            border: 1px solid #2C3E50;
            color: #FFFFFF;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #E67E22;
            background: #3A526B;
        }

        .btn-login {
            width: 100%;
            padding: 0.875rem;
            background: #E67E22;
            color: #FFFFFF;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .btn-login:hover {
            background: #D35400;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(230,126,34,0.3);
        }

        .btn-login:disabled {
            background: #7F8C8D;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #E74C3C;
            color: #FFFFFF;
            padding: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .back-link {
            text-align: center;
            margin-top: 2rem;
        }

        .back-link a {
            color: #E67E22;
            text-decoration: none;
            font-size: 0.9rem;
            transition: opacity 0.3s;
        }

        .back-link a:hover {
            opacity: 0.8;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #FFFFFF;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .password-toggle {
            position: relative;
        }

        .toggle-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #FFFFFF;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s;
        }

        .toggle-btn:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>管理員登入</h1>
            <p>請輸入您的管理員帳號密碼</p>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">帳號</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="form-input" 
                    placeholder="請輸入帳號"
                    required
                    autocomplete="username"
                >
            </div>

            <div class="form-group">
                <label class="form-label" for="password">密碼</label>
                <div class="password-toggle">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="請輸入密碼"
                        required
                        autocomplete="current-password"
                    >
                    <button type="button" class="toggle-btn" id="togglePassword">
                        <span id="toggleIcon">👁️</span>
                    </button>
                </div>
            </div>

            <button type="submit" class="btn-login" id="loginBtn">
                登入
            </button>
        </form>

        <div class="back-link">
            <a href="index.html">← 返回首頁</a>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';

        // 檢查是否已登入
        const token = localStorage.getItem('adminToken');
        if (token) {
            window.location.href = 'admin-panel.html';
        }

        // 密碼顯示切換
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleIcon.textContent = '👁️';
            }
        });

        // 處理登入表單提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // 清除錯誤訊息
            errorMessage.classList.remove('show');
            
            // 顯示載入狀態
            loginBtn.disabled = true;
            loginBtn.innerHTML = '登入中<span class="loading-spinner"></span>';

            try {
                const response = await fetch(`${API_URL}/auth/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    // 儲存 token
                    localStorage.setItem('adminToken', data.token);
                    localStorage.setItem('adminUsername', username);
                    
                    // 跳轉到管理後台
                    window.location.href = 'admin-panel.html';
                } else {
                    errorMessage.textContent = data.message || '登入失敗，請檢查帳號密碼';
                    errorMessage.classList.add('show');
                }
            } catch (error) {
                console.error('登入錯誤:', error);
                errorMessage.textContent = '連接伺服器失敗，請稍後再試';
                errorMessage.classList.add('show');
            } finally {
                // 恢復按鈕狀態
                loginBtn.disabled = false;
                loginBtn.textContent = '登入';
            }
        });
    </script>
</body>
</html> 