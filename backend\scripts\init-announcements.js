const { Announcement } = require('../models');
const { sequelize } = require('../config/database');

async function initAnnouncements() {
    try {
        await sequelize.sync();
        
        // 清除現有公告
        await Announcement.destroy({ where: {} });
        
        // 創建測試公告
        const announcements = [
            {
                title: '歡迎光臨海水不可斗量',
                content: '我們提供最優質的義式手工冰淇淋，精選食材，匠心製作。歡迎品嚐我們的獨特風味！',
                type: 'info',
                is_active: true,
                priority: 10
            },
            {
                title: '夏季限定優惠活動',
                content: '即日起至8月底，購買任意基底加3種配料以上，即享9折優惠！數量有限，售完為止。',
                type: 'promotion',
                is_active: true,
                priority: 20,
                start_date: new Date(),
                end_date: new Date(new Date().setMonth(new Date().getMonth() + 2))
            },
            {
                title: '新品上市：金枕頭榴槤口味',
                content: '泰國進口頂級金枕頭榴槤，濃郁香甜，榴槤控不可錯過的極致享受！',
                type: 'success',
                is_active: true,
                priority: 15
            },
            {
                title: '門市營業時間調整',
                content: '因應夏季需求，營業時間調整為每日 11:00 - 22:00，週五、週六延長至 23:00。',
                type: 'warning',
                is_active: true,
                priority: 5
            }
        ];
        
        // 批量創建公告
        await Announcement.bulkCreate(announcements);
        
        console.log('✅ 公告初始化完成！已創建', announcements.length, '則公告');
        
        // 顯示創建的公告
        const created = await Announcement.findAll({
            order: [['priority', 'DESC']]
        });
        
        console.log('\n📢 當前公告列表：');
        created.forEach(announcement => {
            console.log(`- [${announcement.type.toUpperCase()}] ${announcement.title} (優先級: ${announcement.priority})`);
        });
        
    } catch (error) {
        console.error('❌ 公告初始化失敗:', error);
    } finally {
        await sequelize.close();
    }
}

// 執行初始化
initAnnouncements(); 