const express = require('express');
const router = express.Router();
const { Order, Product } = require('../models');
const { adminAuthMiddleware } = require('./auth');
const { Op } = require('sequelize');
const sevenEleven = require('../services/sevenEleven');
const { exportOrdersToExcel } = require('../utils/exportExcel');
const telegramBot = require('../services/telegramBot');
const { asyncHandler, BusinessError } = require('../middleware/errorHandler');

// 獲取所有訂單（需要管理員權限）
router.get('/', adminAuthMiddleware, async (req, res) => {
  try {
    const { status, startDate, endDate, sevenStoreId } = req.query;
    const where = {};
    
    if (status) {
      where.status = status;
    }
    
    if (sevenStoreId) {
      where.seven_store_id = sevenStoreId;
    }
    
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) where.created_at[Op.lte] = new Date(endDate);
    }
    
    const orders = await Order.findAll({
      where,
      order: [['created_at', 'DESC']]
    });
    
    // 處理訂單資料，加入商品名稱
    const processedOrders = await Promise.all(orders.map(async (order) => {
      const orderData = order.toJSON();
      
      // 解析商品資訊
      if (orderData.items && Array.isArray(orderData.items)) {
        for (let item of orderData.items) {
          // 獲取基底名稱
          if (item.baseId) {
            const baseProduct = await Product.findByPk(item.baseId);
            if (baseProduct) {
              item.baseName = baseProduct.name;
            }
          }
          
          // 獲取配料名稱
          if (item.toppingIds && item.toppingIds.length > 0) {
            const toppings = await Product.findAll({
              where: { id: item.toppingIds }
            });
            item.toppings = toppings.map(t => t.name);
          }
        }
      }
      
      return orderData;
    }));
    
    res.json(processedOrders);
  } catch (error) {
    console.error('獲取訂單錯誤:', error);
    res.status(500).json({ message: '獲取訂單失敗' });
  }
});

// 獲取單一訂單（需要管理員權限）
router.get('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }
    
    const orderData = order.toJSON();
    
    // 解析商品資訊
    if (orderData.items && Array.isArray(orderData.items)) {
      for (let item of orderData.items) {
        if (item.baseId) {
          const baseProduct = await Product.findByPk(item.baseId);
          if (baseProduct) {
            item.baseName = baseProduct.name;
          }
        }
        
        if (item.toppingIds && item.toppingIds.length > 0) {
          const toppings = await Product.findAll({
            where: { id: item.toppingIds }
          });
          item.toppings = toppings.map(t => t.name);
        }
      }
    }
    
    res.json(orderData);
  } catch (error) {
    console.error('獲取訂單錯誤:', error);
    res.status(500).json({ message: '獲取訂單失敗' });
  }
});

// 建立新訂單（公開）
router.post('/', asyncHandler(async (req, res) => {
  const {
    customer_name,
    customer_email,
    customer_phone,
    seven_store_id,
    items,
    notes
  } = req.body;

  // 驗證必填欄位
  if (!customer_name || !customer_email || !customer_phone || !seven_store_id || !items || !Array.isArray(items) || items.length === 0) {
    throw new BusinessError('請填寫所有必填欄位並至少選擇一項商品');
  }

  // 驗證電子郵件格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(customer_email)) {
    throw new BusinessError('請輸入有效的電子郵件格式');
  }

  // 驗證手機號碼格式
  const phoneRegex = /^09\d{8}$/;
  if (!phoneRegex.test(customer_phone)) {
    throw new BusinessError('請輸入有效的手機號碼格式 (09xxxxxxxx)');
  }

  // 驗證 7-11 門市
  const store = sevenEleven.getStoreById(seven_store_id);
  if (!store) {
    throw new BusinessError('無效的 7-11 門市代號');
  }
    
    // 計算總金額並驗證商品
    let totalAmount = 0;
    const processedItems = [];
    
    for (const item of items) {
      // 驗證商品項目格式
      if (!item.baseId || !item.quantity || item.quantity <= 0 || item.quantity > 10) {
        throw new BusinessError('商品項目格式錯誤或數量無效 (1-10)');
      }

      const base = await Product.findByPk(item.baseId);
      if (!base || base.type !== 'base') {
        throw new BusinessError('無效的基底口味');
      }

      // 檢查庫存
      if (base.stock < item.quantity) {
        throw new BusinessError(`${base.name} 庫存不足，剩餘 ${base.stock} 份`);
      }
      
      let itemPrice = parseFloat(base.price);
      const toppingIds = [];
      const toppingNames = [];
      
      if (item.toppingIds && item.toppingIds.length > 0) {
        for (const toppingId of item.toppingIds) {
          const topping = await Product.findByPk(toppingId);
          if (topping && topping.type === 'topping') {
            itemPrice += parseFloat(topping.price);
            toppingIds.push(toppingId);
            toppingNames.push(topping.name);
          }
        }
      }
      
      itemPrice *= item.quantity;
      totalAmount += itemPrice;
      
      processedItems.push({
        baseId: item.baseId,
        baseName: base.name,
        toppingIds,
        toppings: toppingNames,
        quantity: item.quantity,
        itemPrice
      });
      
      // 更新庫存
      await base.update({ stock: base.stock - item.quantity });
    }
    
    // 創建訂單
    const order = await Order.create({
      customer_name,
      customer_email,
      customer_phone,
      seven_store_id: store.id,
      seven_store_name: store.name,
      items: processedItems,
      total_amount: totalAmount,
      payment_method: 'cash_on_delivery',
      notes
    });
    
    // 發送 Telegram 通知
    try {
      await telegramBot.sendNewOrderNotification(order.toJSON());
    } catch (telegramError) {
      console.warn('Telegram 通知發送失敗:', telegramError.message);
      // 不影響訂單創建，只記錄警告
    }

    res.status(201).json(order);
}));

// 更新訂單狀態（需要管理員權限）
router.patch('/:id/status', adminAuthMiddleware, async (req, res) => {
  try {
    const { status } = req.body;
    
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }
    
    // 如果訂單被取消，恢復庫存
    if (status === 'cancelled' && order.status !== 'cancelled') {
      const items = order.items;
      for (const item of items) {
        if (item.baseId) {
          const product = await Product.findByPk(item.baseId);
          if (product) {
            await product.update({ stock: product.stock + item.quantity });
          }
        }
      }
    }
    
    const oldStatus = order.status;
    await order.update({ status });
    
    // 發送 Telegram 通知
    await telegramBot.sendOrderStatusUpdate(order.toJSON(), oldStatus, status);
    
    res.json(order);
  } catch (error) {
    console.error('更新訂單狀態錯誤:', error);
    res.status(400).json({ message: '更新訂單狀態失敗' });
  }
});

// 更新付款狀態（需要管理員權限）
router.patch('/:id/payment-status', adminAuthMiddleware, async (req, res) => {
  try {
    const { payment_status } = req.body;
    
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }
    
    await order.update({ payment_status });
    
    // 發送 Telegram 通知
    await telegramBot.sendPaymentStatusUpdate(order.toJSON(), payment_status);
    
    res.json(order);
  } catch (error) {
    console.error('更新付款狀態錯誤:', error);
    res.status(400).json({ message: '更新付款狀態失敗' });
  }
});

// 批量匯出訂單為 Excel（需要管理員權限）
router.post('/export', adminAuthMiddleware, async (req, res) => {
  try {
    const { orderIds, filters } = req.body;
    
    const result = await exportOrdersToExcel(orderIds, filters);
    
    // 設定回應標頭
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
    
    res.send(result.buffer);
  } catch (error) {
    console.error('匯出 Excel 錯誤:', error);
    res.status(500).json({ message: '匯出失敗: ' + error.message });
  }
});

// 訂單統計（需要管理員權限）
router.get('/stats/summary', adminAuthMiddleware, async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // 今日訂單數
    const todayOrders = await Order.count({
      where: {
        created_at: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        }
      }
    });
    
    // 今日營收
    const todayRevenueResult = await Order.sum('total_amount', {
      where: {
        created_at: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        },
        payment_status: 'paid'
      }
    });
    
    // 待處理訂單
    const pendingOrders = await Order.count({
      where: { status: 'pending' }
    });
    
    // 本月訂單數
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthOrders = await Order.count({
      where: {
        created_at: {
          [Op.gte]: monthStart
        }
      }
    });
    
    res.json({
      todayOrders,
      todayRevenue: todayRevenueResult || 0,
      pendingOrders,
      monthOrders
    });
  } catch (error) {
    console.error('獲取統計資料錯誤:', error);
    res.status(500).json({ message: '獲取統計資料失敗' });
  }
});

// 刪除訂單（需要管理員權限，僅限已取消的訂單）
router.delete('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }
    
    if (order.status !== 'cancelled') {
      return res.status(400).json({ message: '只能刪除已取消的訂單' });
    }
    
    await order.destroy();
    res.json({ message: '訂單已刪除' });
  } catch (error) {
    console.error('刪除訂單錯誤:', error);
    res.status(500).json({ message: '刪除訂單失敗' });
  }
});

module.exports = router; 