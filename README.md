# 🍦 海水不可斗量 - 義式手工冰淇淋店管理系統

一個功能完整的冰淇淋線上訂購與後台管理系統，專為「海水不可斗量」義式手工冰淇淋店打造。

## 📋 系統特色

### 客戶端功能
- 🍨 10種精選基底口味
- 🍓 30種配料自由搭配（6大分類）
- 📢 公告系統顯示最新優惠與通知
- 🛒 直覺式購物車介面
- 🏪 7-11門市取貨整合
- 📱 **全新手機版優化體驗**

### 管理端功能
- 🔐 隱藏式管理員入口（Logo點擊5次）
- 📦 產品管理（多圖上傳、庫存追蹤）
- 📋 訂單管理（狀態更新、Excel匯出）
- 💸 優惠折扣設定
- 📢 公告管理系統
- 🤖 Telegram Bot即時通知
- 📊 每日營業報告

## 📱 手機版優化特色

### 觸控體驗優化
- 👆 橫向滾動的產品選擇
- 📌 底部固定選擇欄即時顯示
- 🎯 44px最小觸控區域
- 💫 流暢的動畫過渡效果

### 手勢操作支援
- ➡️ 右滑關閉購物車
- 📜 自然的滾動體驗
- 🔄 防止誤觸的雙擊縮放
- 📱 支援安裝到主螢幕(PWA)

### 視覺與佈局
- 🌓 自動深色模式支援
- 📐 iPhone瀏海屏安全區域適配
- 🖼️ 優化的產品卡片展示
- ⚡ 毛玻璃效果的固定導航

### 表單與輸入
- 📝 防止iOS自動縮放
- ⌨️ 自動滾動到輸入框
- 🔒 全屏模態視窗設計
- ✅ 大型觸控按鈕

詳細的手機版優化指南請參考 [MOBILE_OPTIMIZATION.md](./MOBILE_OPTIMIZATION.md)

## 🛠️ 技術架構

### 後端技術
- **框架**: Node.js + Express.js
- **資料庫**: SQLite + Sequelize ORM
- **認證**: JWT (JSON Web Token)
- **圖片處理**: Sharp (WebP轉換、縮圖生成)
- **通知**: Telegram Bot API
- **檔案處理**: ExcelJS (訂單匯出)

### 前端技術
- **純原生**: HTML5 + CSS3 + JavaScript
- **UI框架**: 自訂響應式設計
- **圖標**: Font Awesome
- **API通訊**: Fetch API

## 🚀 快速開始

### 1. 進入後端目錄
```bash
cd backend
```

### 2. 安裝依賴套件
```bash
npm install
```

### 3. 啟動伺服器
```bash
npm start
```
伺服器將在 http://localhost:3000 啟動

### 4. 開啟網頁
- **客戶訂購**: 開啟 `customer-app.html`
- **管理後台**: 開啟 `index.html` → 點擊Logo 5次
  - 預設帳號: `admin`
  - 預設密碼: `admin123`

### 5. 初始化測試資料（選用）
```bash
# 初始化產品資料
curl -X POST http://localhost:3000/api/products/init-samples

# 初始化公告資料
node scripts/init-announcements.js
```

## 📚 API 文檔

### 認證相關
| 方法 | 端點 | 說明 |
|------|------|------|
| POST | `/api/auth/admin-login` | 管理員登入 |
| GET | `/api/auth/verify` | 驗證Token |

### 產品管理
| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/api/products` | 取得所有產品 |
| POST | `/api/products` | 新增產品 |
| PUT | `/api/products/:id` | 更新產品 |
| DELETE | `/api/products/:id` | 刪除產品 |
| POST | `/api/products/upload-images` | 上傳產品圖片 |
| PATCH | `/api/products/batch-update-stock` | 批量更新庫存 |

### 訂單管理
| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/api/orders` | 取得訂單列表 |
| GET | `/api/orders/:id` | 取得訂單詳情 |
| POST | `/api/orders` | 建立新訂單 |
| PATCH | `/api/orders/:id/status` | 更新訂單狀態 |
| GET | `/api/orders/export` | 匯出Excel |

### 公告管理
| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/api/announcements/active` | 取得活躍公告 |
| GET | `/api/announcements/admin` | 取得所有公告 |
| POST | `/api/announcements` | 新增公告 |
| PUT | `/api/announcements/:id` | 更新公告 |
| DELETE | `/api/announcements/:id` | 刪除公告 |

### 7-11門市
| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/api/seven-eleven/stores` | 取得門市列表 |
| GET | `/api/seven-eleven/cities` | 取得城市列表 |
| GET | `/api/seven-eleven/search` | 搜尋門市 |

### Telegram通知
| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/api/telegram/config` | 取得配置 |
| POST | `/api/telegram/config` | 更新配置 |
| POST | `/api/telegram/test` | 測試連接 |
| POST | `/api/telegram/daily-report` | 發送每日報告 |

## 📁 專案結構

```
ice-cream-shop/
├── backend/                 # 後端程式碼
│   ├── config/             # 配置檔案
│   ├── database/           # SQLite資料庫
│   ├── middleware/         # 中介軟體
│   ├── models/             # 資料模型
│   ├── routes/             # API路由
│   ├── services/           # 商業邏輯
│   ├── uploads/            # 上傳檔案
│   └── server.js           # 主程式
├── admin-dashboard.html    # 管理員登入頁
├── admin-panel.html        # 管理後台
├── customer-app.html       # 客戶訂購頁
├── index.html              # 首頁
└── START_GUIDE.md          # 快速啟動指南
```

## 🔧 開發進度

### ✅ 已完成功能

1. **資料庫系統** - SQLite + Sequelize ORM
2. **管理員系統** - JWT認證、隱藏入口
3. **產品管理** - 多圖上傳、庫存管理、優惠系統
4. **訂單系統** - 7-11整合、Excel匯出
5. **通知系統** - Telegram Bot整合
6. **公告系統** - 多類型公告、優先級排序

### 🔄 待開發功能

- [ ] 整合真實7-11門市API
- [ ] 客戶訂單追蹤系統
- [ ] 自動化每日報告（node-cron）
- [ ] 多語言支援
- [ ] 會員系統（積分、優惠券）
- [ ] 數據分析儀表板
- [ ] LINE通知整合

## 📝 注意事項

1. **首次使用**：系統會自動建立預設管理員帳號
2. **圖片上傳**：支援jpg/jpeg/png，自動轉換為WebP格式
3. **訂單編號**：格式為 ORD{年}{日}{月}{時}{分}{秒}{隨機3碼}
4. **Excel檔名**：格式為 DOC{年}{日}{月}.xlsx
5. **Telegram設定**：需先建立Bot並取得Token和ChatID

## 🐛 疑難排解

### 無法啟動伺服器
- 確認已安裝Node.js (建議v14以上)
- 確認已在backend目錄執行 `npm install`
- 檢查3000端口是否被占用

### 找不到管理員入口
- 在首頁（index.html）點擊Logo「海水不可斗量」5次
- 確保JavaScript已啟用

### Telegram通知未收到
- 檢查Bot Token是否正確
- 確認Chat ID設定無誤
- 使用測試功能驗證連接

## 📞 技術支援

遇到問題時請檢查：
1. 伺服器控制台錯誤訊息
2. 瀏覽器開發者工具Console
3. 網路請求狀態（Network面板）

---

🌟 **海水不可斗量** - 精選食材・匠心製作・獨特風味 