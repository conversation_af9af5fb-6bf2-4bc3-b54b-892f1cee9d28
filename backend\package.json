{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-telegram-bot-api": "^0.66.0", "sequelize": "^6.37.3", "sharp": "^0.34.2", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}