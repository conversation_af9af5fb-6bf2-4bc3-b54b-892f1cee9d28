const express = require('express');
const router = express.Router();
const { Discount, Product } = require('../models');
const { adminAuthMiddleware } = require('./auth');
const { Op } = require('sequelize');

// Validation middleware for discounts
const validateDiscount = (req, res, next) => {
  const { name, type, rules } = req.body;

  if (!name || !type) {
    return res.status(400).json({ message: '必要欄位缺失: name, type' });
  }
  
  if (type !== 'bundle' && (!rules || typeof rules !== 'object')) {
     return res.status(400).json({ message: 'rules 必須是一個 JSON 物件' });
  }

  switch (type) {
    case 'percentage':
      if (typeof rules.percent !== 'number' || rules.percent <= 0 || rules.percent > 100) {
        return res.status(400).json({ message: '百分比優惠的 "rules" 必須包含 "percent" 欄位，其值需介於 0 和 100 之間' });
      }
      break;
    case 'fixed':
      if (typeof rules.amount !== 'number' || rules.amount <= 0) {
        return res.status(400).json({ message: '固定金額優惠的 "rules" 必須包含一個正數 "amount"' });
      }
      break;
    case 'quantity':
      if (typeof rules.min_quantity !== 'number' || rules.min_quantity <= 1) {
        return res.status(400).json({ message: '數量優惠的 "rules" 必須包含 "min_quantity" 欄位，其值需大於 1' });
      }
      if (typeof rules.percent !== 'number' || rules.percent <= 0 || rules.percent > 100) {
        return res.status(400).json({ message: '數量優惠的 "rules" 必須包含 "percent" 欄位，其值需介於 0 和 100 之間' });
      }
      break;
    case 'bundle':
       if (!req.body.product_ids || !Array.isArray(req.body.product_ids) || req.body.product_ids.length < 2) {
        return res.status(400).json({ message: '組合優惠必須包含 "product_ids" 陣列 (至少2個產品)' });
      }
      if (!rules || typeof rules.price !== 'number' || rules.price <= 0) {
        return res.status(400).json({ message: '組合優惠的 "rules" 必須包含一個正數 "price"' });
      }
      break;
    default:
      return res.status(400).json({ message: `無效的優惠類型: ${type}` });
  }

  next();
};

// 獲取所有優惠（管理員）
router.get('/admin', adminAuthMiddleware, async (req, res) => {
  try {
    const discounts = await Discount.findAll({
      order: [['is_active', 'DESC'], ['created_at', 'DESC']]
    });
    res.json(discounts);
  } catch (error) {
    console.error('獲取所有優惠錯誤:', error);
    res.status(500).json({ message: '獲取優惠失敗' });
  }
});

// 獲取所有優惠（公開）
router.get('/', async (req, res) => {
  try {
    const { active } = req.query;
    const where = {};
    
    if (active === 'true') {
      const now = new Date();
      where.is_active = true;
      where[Op.or] = [
        { start_date: null, end_date: null },
        { start_date: { [Op.lte]: now }, end_date: null },
        { start_date: null, end_date: { [Op.gte]: now } },
        { start_date: { [Op.lte]: now }, end_date: { [Op.gte]: now } }
      ];
    }
    
    const discounts = await Discount.findAll({
      where,
      order: [['priority', 'DESC'], ['created_at', 'DESC']]
    });
    
    res.json(discounts);
  } catch (error) {
    console.error('獲取優惠錯誤:', error);
    res.status(500).json({ message: '獲取優惠失敗' });
  }
});

// 獲取單一優惠（公開）
router.get('/:id', async (req, res) => {
  try {
    const discount = await Discount.findByPk(req.params.id);
    if (!discount) {
      return res.status(404).json({ message: '優惠不存在' });
    }
    res.json(discount);
  } catch (error) {
    res.status(500).json({ message: '獲取優惠失敗' });
  }
});

// 新增優惠（需要管理員權限）
router.post('/', adminAuthMiddleware, validateDiscount, async (req, res) => {
  try {
    const discount = await Discount.create(req.body);
    res.status(201).json(discount);
  } catch (error) {
    console.error('新增優惠錯誤:', error);
    res.status(400).json({ message: error.message });
  }
});

// 更新優惠（需要管理員權限）
router.put('/:id', adminAuthMiddleware, validateDiscount, async (req, res) => {
  try {
    const discount = await Discount.findByPk(req.params.id);
    if (!discount) {
      return res.status(404).json({ message: '優惠不存在' });
    }
    
    await discount.update(req.body);
    res.json(discount);
  } catch (error) {
    console.error('更新優惠錯誤:', error);
    res.status(400).json({ message: error.message });
  }
});

// 刪除優惠（需要管理員權限）
router.delete('/:id', adminAuthMiddleware, async (req, res) => {
  try {
    const discount = await Discount.findByPk(req.params.id);
    if (!discount) {
      return res.status(404).json({ message: '優惠不存在' });
    }
    
    await discount.destroy();
    res.json({ message: '優惠已刪除' });
  } catch (error) {
    console.error('刪除優惠錯誤:', error);
    res.status(500).json({ message: '刪除優惠失敗' });
  }
});

// 切換優惠狀態（需要管理員權限）
router.patch('/:id/toggle', adminAuthMiddleware, async (req, res) => {
  try {
    const discount = await Discount.findByPk(req.params.id);
    if (!discount) {
      return res.status(404).json({ message: '優惠不存在' });
    }
    
    await discount.update({ is_active: !discount.is_active });
    res.json({ message: '優惠狀態已更新', is_active: discount.is_active });
  } catch (error) {
    res.status(500).json({ message: '更新優惠狀態失敗' });
  }
});

// 計算優惠價格（公開）
router.post('/calculate', async (req, res) => {
  try {
    const { items } = req.body; // [{productId, quantity}, ...]
    
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ message: '請提供商品項目' });
    }
    
    // 獲取所有有效優惠
    const now = new Date();
    const activeDiscounts = await Discount.findAll({
      where: {
        is_active: true,
        [Op.or]: [
          { start_date: null, end_date: null },
          { start_date: { [Op.lte]: now }, end_date: null },
          { start_date: null, end_date: { [Op.gte]: now } },
          { start_date: { [Op.lte]: now }, end_date: { [Op.gte]: now } }
        ]
      },
      order: [['priority', 'DESC']]
    });
    
    // 計算原價
    let originalPrice = 0;
    const productDetails = [];
    
    for (const item of items) {
      const product = await Product.findByPk(item.productId);
      if (product) {
        const subtotal = product.price * item.quantity;
        originalPrice += subtotal;
        productDetails.push({
          productId: product.id,
          name: product.name,
          price: product.price,
          quantity: item.quantity,
          subtotal
        });
      }
    }
    
    // 應用優惠
    let finalPrice = originalPrice;
    const appliedDiscounts = [];
    
    for (const discount of activeDiscounts) {
      let discountAmount = 0;
      let applicable = false;
      
      switch (discount.type) {
        case 'quantity':
          // 數量優惠：買X件打Y折
          const rules = discount.rules;
          const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
          
          if (rules.min_quantity && totalQuantity >= rules.min_quantity) {
            if (rules.percent) {
              discountAmount = originalPrice * (rules.percent / 100);
              applicable = true;
            }
          }
          break;
          
        case 'percentage':
          // 百分比折扣
          if (discount.rules.percent) {
            discountAmount = originalPrice * (discount.rules.percent / 100);
            applicable = true;
          }
          break;
          
        case 'fixed':
          // 固定金額折扣
          if (discount.rules.amount) {
            discountAmount = Math.min(discount.rules.amount, originalPrice);
            applicable = true;
          }
          break;
          
        case 'bundle':
          // 組合優惠（需要更複雜的邏輯）
          // 暫時略過
          break;
      }
      
      if (applicable && discountAmount > 0) {
        finalPrice -= discountAmount;
        appliedDiscounts.push({
          id: discount.id,
          name: discount.name,
          type: discount.type,
          discountAmount
        });
      }
    }
    
    res.json({
      originalPrice,
      finalPrice: Math.max(0, finalPrice),
      discount: originalPrice - finalPrice,
      items: productDetails,
      appliedDiscounts
    });
    
  } catch (error) {
    console.error('計算優惠錯誤:', error);
    res.status(500).json({ message: '計算優惠失敗' });
  }
});

// 初始化範例優惠（需要管理員權限）
router.post('/init-samples', adminAuthMiddleware, async (req, res) => {
  try {
    const sampleDiscounts = [
      {
        name: '週年慶優惠',
        description: '全館商品9折優惠',
        type: 'percentage',
        rules: { percent: 10 },
        is_active: true,
        priority: 10
      },
      {
        name: '買三送一',
        description: '任選4件商品，只需付3件的價格（等同於75折，此處為方便計算設為25%折扣）',
        type: 'quantity',
        rules: { min_quantity: 4, percent: 25 },
        is_active: true,
        priority: 20
      },
      {
        name: '新會員優惠',
        description: '首次購買折抵50元',
        type: 'fixed',
        rules: { amount: 50 },
        is_active: true,
        priority: 5
      }
    ];
    
    const created = await Discount.bulkCreate(sampleDiscounts);
    
    res.json({
      message: '範例優惠已建立',
      count: created.length
    });
  } catch (error) {
    console.error('初始化範例優惠錯誤:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router; 