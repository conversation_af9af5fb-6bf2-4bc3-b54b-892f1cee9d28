<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#E67E22">
    <title>海水不可斗量 - 線上訂購</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #E67E22;
            --primary-dark: #D35400;
            --secondary: #34495E;
            --success: #27AE60;
            --danger: #E74C3C;
            --warning: #F39C12;
            --info: #3498DB;
            --light: #ECF0F1;
            --dark: #2C3E50;
            --white: #FFFFFF;
            --border: #BDC3C7;
            --text: #2C3E50;
            --text-light: #7F8C8D;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: var(--text);
            line-height: 1.6;
        }

        /* 載入動畫 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--light);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 頂部導航 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding-top: env(safe-area-inset-top);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .cart-btn {
            position: relative;
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cart-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger);
            color: var(--white);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 700;
        }

        /* 主要內容 */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 公告區域 */
        .announcements {
            margin-bottom: 2rem;
        }

        .announcement {
            background: var(--white);
            border-left: 4px solid var(--info);
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .announcement.warning { border-left-color: var(--warning); }
        .announcement.success { border-left-color: var(--success); }
        .announcement.promotion { border-left-color: var(--primary); }

        .announcement-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text);
        }

        .announcement-content {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* 產品選擇區域 */
        .product-section {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-icon {
            color: var(--primary);
        }

        /* 基底選擇 */
        .base-selection {
            margin-bottom: 2rem;
        }

        .base-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        /* 手機版橫向滾動 */
        @media (max-width: 768px) {
            .base-grid {
                display: flex;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scroll-snap-type: x mandatory;
                gap: 1rem;
                padding: 0 1rem;
                margin: 0 -1rem;
            }

            .base-card {
                min-width: 280px;
                scroll-snap-align: center;
                flex-shrink: 0;
            }
        }

        .base-card {
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .base-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(230, 126, 34, 0.2);
        }

        .base-card.selected {
            border-color: var(--primary);
            background: rgba(230, 126, 34, 0.05);
        }

        .base-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--text);
        }

        .base-price {
            color: var(--primary);
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .base-description {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .base-stock {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .base-stock.low {
            color: var(--warning);
            font-weight: 600;
        }

        .base-stock.out {
            color: var(--danger);
            font-weight: 600;
        }

        /* 配料選擇 */
        .topping-selection {
            margin-bottom: 2rem;
        }

        .topping-categories {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .category-btn {
            background: var(--light);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .category-btn.active {
            background: var(--primary);
            color: var(--white);
        }

        .topping-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .topping-card {
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--white);
            position: relative;
        }

        .topping-card:hover {
            border-color: var(--primary);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .topping-card.selected {
            border-color: var(--primary);
            background: rgba(230, 126, 34, 0.05);
        }

        .topping-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .topping-price {
            color: var(--primary);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .selected-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--primary);
            color: var(--white);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .topping-card.selected .selected-indicator {
            opacity: 1;
        }

        /* 數量選擇 */
        .quantity-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .quantity-label {
            font-weight: 600;
            color: var(--text);
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quantity-btn {
            background: var(--primary);
            color: var(--white);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: var(--primary-dark);
        }

        .quantity-btn:disabled {
            background: var(--border);
            cursor: not-allowed;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 2px solid var(--border);
            border-radius: 8px;
            padding: 0.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* 價格顯示 */
        .price-summary {
            background: var(--light);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .price-row.total {
            border-top: 2px solid var(--border);
            padding-top: 0.5rem;
            margin-top: 1rem;
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary);
        }

        /* 加入購物車按鈕 */
        .add-to-cart-btn {
            width: 100%;
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .add-to-cart-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .add-to-cart-btn:disabled {
            background: var(--border);
            cursor: not-allowed;
            transform: none;
        }

        /* 手機版底部固定選擇欄 */
        .mobile-selection-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid var(--border);
            padding: 1rem;
            padding-bottom: calc(1rem + env(safe-area-inset-bottom));
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: none;
        }

        .mobile-selection-bar.show {
            transform: translateY(0);
        }

        .selection-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .selection-info {
            flex: 1;
        }

        .selection-name {
            font-weight: 600;
            color: var(--text);
            margin-bottom: 0.25rem;
        }

        .selection-price {
            color: var(--primary);
            font-weight: 700;
            font-size: 1.1rem;
        }

        .mobile-add-btn {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .mobile-add-btn:hover {
            background: var(--primary-dark);
        }

        .mobile-add-btn:disabled {
            background: var(--border);
            cursor: not-allowed;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .header-content {
                padding: 1rem;
            }

            .main-content {
                padding: 1rem;
                padding-bottom: 120px; /* 為底部欄留空間 */
            }

            .topping-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .topping-categories {
                justify-content: center;
            }

            .quantity-section {
                justify-content: center;
            }

            .mobile-selection-bar {
                display: block;
            }

            .add-to-cart-btn {
                display: none; /* 隱藏原按鈕，使用底部欄 */
            }

            /* 觸控優化 */
            .base-card,
            .topping-card,
            .category-btn,
            .quantity-btn {
                min-height: 44px;
                -webkit-tap-highlight-color: transparent;
            }

            .base-card:active,
            .topping-card:active {
                transform: scale(0.98);
            }

            /* 移除 hover 效果 */
            .base-card:hover,
            .topping-card:hover,
            .cart-btn:hover,
            .add-to-cart-btn:hover,
            .mobile-add-btn:hover {
                transform: none;
            }
        }

        /* 隱藏元素 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 載入動畫 -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <!-- 頂部導航 -->
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <i class="fas fa-ice-cream"></i>
                海水不可斗量
            </a>
            <button class="cart-btn" id="cartBtn">
                <i class="fas fa-shopping-cart"></i>
                購物車
                <span class="cart-count hidden" id="cartCount">0</span>
            </button>
        </div>
    </header>

    <!-- 主要內容 -->
    <main class="main-content">
        <!-- 公告區域 -->
        <section class="announcements" id="announcements">
            <!-- 公告將通過 JavaScript 動態載入 -->
        </section>

        <!-- 產品選擇區域 -->
        <section class="product-section">
            <!-- 基底選擇 -->
            <div class="base-selection">
                <h2 class="section-title">
                    <i class="fas fa-ice-cream section-icon"></i>
                    選擇基底口味 <span style="color: var(--danger);">*</span>
                </h2>
                <div class="base-grid" id="baseGrid">
                    <!-- 基底選項將通過 JavaScript 動態載入 -->
                </div>
            </div>

            <!-- 配料選擇 -->
            <div class="topping-selection">
                <h2 class="section-title">
                    <i class="fas fa-plus-circle section-icon"></i>
                    選擇配料 <span style="color: var(--text-light); font-size: 0.9rem;">(可選)</span>
                </h2>
                
                <div class="topping-categories" id="toppingCategories">
                    <!-- 配料分類按鈕將通過 JavaScript 動態載入 -->
                </div>
                
                <div class="topping-grid" id="toppingGrid">
                    <!-- 配料選項將通過 JavaScript 動態載入 -->
                </div>
            </div>

            <!-- 數量選擇 -->
            <div class="quantity-section">
                <span class="quantity-label">數量：</span>
                <div class="quantity-controls">
                    <button class="quantity-btn" id="decreaseBtn">-</button>
                    <input type="number" class="quantity-input" id="quantityInput" value="1" min="1" max="10" readonly>
                    <button class="quantity-btn" id="increaseBtn">+</button>
                </div>
            </div>

            <!-- 價格摘要 -->
            <div class="price-summary" id="priceSummary">
                <div class="price-row">
                    <span>基底：</span>
                    <span id="basePrice">$0</span>
                </div>
                <div class="price-row">
                    <span>配料：</span>
                    <span id="toppingPrice">$0</span>
                </div>
                <div class="price-row">
                    <span>數量：</span>
                    <span id="quantityDisplay">1</span>
                </div>
                <div class="price-row total">
                    <span>總計：</span>
                    <span id="totalPrice">$0</span>
                </div>
            </div>

            <!-- 加入購物車按鈕 -->
            <button class="add-to-cart-btn" id="addToCartBtn" disabled>
                <i class="fas fa-cart-plus"></i>
                加入購物車
            </button>
        </section>
    </main>

    <!-- 手機版底部固定選擇欄 -->
    <div class="mobile-selection-bar" id="mobileSelectionBar">
        <div class="selection-summary">
            <div class="selection-info">
                <div class="selection-name" id="mobileSelectionName">請選擇基底口味</div>
                <div class="selection-price" id="mobileSelectionPrice">$0</div>
            </div>
            <button class="mobile-add-btn" id="mobileAddBtn" disabled>
                加入購物車
            </button>
        </div>
    </div>

    <script>
        // API 基礎 URL
        const API_URL = 'http://localhost:3001/api';
        
        // 全域變數
        let products = [];
        let selectedBase = null;
        let selectedToppings = [];
        let quantity = 1;
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        // DOM 元素
        const loading = document.getElementById('loading');
        const cartBtn = document.getElementById('cartBtn');
        const cartCount = document.getElementById('cartCount');
        const baseGrid = document.getElementById('baseGrid');
        const toppingCategories = document.getElementById('toppingCategories');
        const toppingGrid = document.getElementById('toppingGrid');
        const quantityInput = document.getElementById('quantityInput');
        const decreaseBtn = document.getElementById('decreaseBtn');
        const increaseBtn = document.getElementById('increaseBtn');
        const addToCartBtn = document.getElementById('addToCartBtn');
        const mobileSelectionBar = document.getElementById('mobileSelectionBar');
        const mobileSelectionName = document.getElementById('mobileSelectionName');
        const mobileSelectionPrice = document.getElementById('mobileSelectionPrice');
        const mobileAddBtn = document.getElementById('mobileAddBtn');
        
        // 載入公告
        async function loadAnnouncements() {
            try {
                const response = await fetch(`${API_URL}/announcements/active`);
                const announcements = await response.json();

                const announcementsContainer = document.getElementById('announcements');

                if (announcements.length === 0) {
                    announcementsContainer.style.display = 'none';
                    return;
                }

                announcementsContainer.innerHTML = announcements.map(announcement => `
                    <div class="announcement ${announcement.type}">
                        <div class="announcement-title">${announcement.title}</div>
                        <div class="announcement-content">${announcement.content}</div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('載入公告錯誤:', error);
            }
        }

        // 載入產品
        async function loadProducts() {
            try {
                const response = await fetch(`${API_URL}/products?available=true`);
                products = await response.json();

                renderBases();
                renderToppingCategories();
                renderToppings();
            } catch (error) {
                console.error('載入產品錯誤:', error);
                throw error;
            }
        }

        // 渲染基底選項
        function renderBases() {
            const bases = products.filter(p => p.type === 'base');

            baseGrid.innerHTML = bases.map(base => `
                <div class="base-card" data-id="${base.id}" onclick="selectBase(${base.id})">
                    <div class="base-name">${base.name}</div>
                    <div class="base-price">$${base.price}</div>
                    <div class="base-description">${base.description || ''}</div>
                    <div class="base-stock ${base.stock <= 10 ? 'low' : ''} ${base.stock === 0 ? 'out' : ''}">
                        ${base.stock === 0 ? '已售完' : base.stock <= 10 ? `剩餘 ${base.stock} 份` : '充足庫存'}
                    </div>
                </div>
            `).join('');
        }

        // 渲染配料分類
        function renderToppingCategories() {
            const toppings = products.filter(p => p.type === 'topping');
            const categories = [...new Set(toppings.map(t => t.category))].filter(Boolean);

            toppingCategories.innerHTML = `
                <button class="category-btn active" onclick="filterToppings('all')">全部</button>
                ${categories.map(category => `
                    <button class="category-btn" onclick="filterToppings('${category}')">${category}</button>
                `).join('')}
            `;
        }

        // 渲染配料選項
        function renderToppings(filterCategory = 'all') {
            const toppings = products.filter(p => p.type === 'topping');
            const filteredToppings = filterCategory === 'all'
                ? toppings
                : toppings.filter(t => t.category === filterCategory);

            toppingGrid.innerHTML = filteredToppings.map(topping => `
                <div class="topping-card ${selectedToppings.includes(topping.id) ? 'selected' : ''}"
                     data-id="${topping.id}" onclick="toggleTopping(${topping.id})">
                    <div class="topping-name">${topping.name}</div>
                    <div class="topping-price">+$${topping.price}</div>
                    <div class="selected-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            `).join('');
        }

        // 選擇基底
        function selectBase(baseId) {
            const base = products.find(p => p.id === baseId);
            if (!base || base.stock === 0) return;

            selectedBase = base;

            // 更新 UI
            document.querySelectorAll('.base-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-id="${baseId}"]`).classList.add('selected');

            updatePriceSummary();
            updateAddToCartButton();
        }

        // 切換配料選擇
        function toggleTopping(toppingId) {
            const index = selectedToppings.indexOf(toppingId);

            if (index > -1) {
                selectedToppings.splice(index, 1);
            } else {
                selectedToppings.push(toppingId);
            }

            // 更新 UI
            const card = document.querySelector(`[data-id="${toppingId}"]`);
            card.classList.toggle('selected');

            updatePriceSummary();
        }

        // 篩選配料
        function filterToppings(category) {
            // 更新分類按鈕狀態
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            renderToppings(category);
        }

        // 更新價格摘要
        function updatePriceSummary() {
            const basePrice = selectedBase ? parseFloat(selectedBase.price) : 0;
            const toppingPrice = selectedToppings.reduce((sum, toppingId) => {
                const topping = products.find(p => p.id === toppingId);
                return sum + (topping ? parseFloat(topping.price) : 0);
            }, 0);

            const itemTotal = (basePrice + toppingPrice) * quantity;

            document.getElementById('basePrice').textContent = `$${basePrice}`;
            document.getElementById('toppingPrice').textContent = `$${toppingPrice}`;
            document.getElementById('quantityDisplay').textContent = quantity;
            document.getElementById('totalPrice').textContent = `$${itemTotal}`;

            // 更新手機版選擇欄
            updateMobileSelectionBar(basePrice, toppingPrice, itemTotal);
        }

        // 更新手機版選擇欄
        function updateMobileSelectionBar(basePrice, toppingPrice, itemTotal) {
            if (selectedBase) {
                const toppingNames = selectedToppings.map(id => {
                    const topping = products.find(p => p.id === id);
                    return topping ? topping.name : '';
                }).filter(Boolean);

                const displayName = selectedBase.name +
                    (toppingNames.length > 0 ? ` + ${toppingNames.join(', ')}` : '');

                mobileSelectionName.textContent = displayName;
                mobileSelectionPrice.textContent = `$${itemTotal}`;
                mobileSelectionBar.classList.add('show');
                mobileAddBtn.disabled = false;
            } else {
                mobileSelectionName.textContent = '請選擇基底口味';
                mobileSelectionPrice.textContent = '$0';
                mobileSelectionBar.classList.remove('show');
                mobileAddBtn.disabled = true;
            }
        }

        // 更新加入購物車按鈕狀態
        function updateAddToCartButton() {
            addToCartBtn.disabled = !selectedBase;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await loadAnnouncements();
                await loadProducts();
                updateCartDisplay();
                setupEventListeners();
            } catch (error) {
                console.error('初始化錯誤:', error);
                alert('載入失敗，請重新整理頁面');
            } finally {
                loading.style.display = 'none';
            }
        });

        // 設置事件監聽器
        function setupEventListeners() {
            // 數量控制
            decreaseBtn.addEventListener('click', () => {
                if (quantity > 1) {
                    quantity--;
                    quantityInput.value = quantity;
                    updatePriceSummary();
                }
            });

            increaseBtn.addEventListener('click', () => {
                if (quantity < 10) {
                    quantity++;
                    quantityInput.value = quantity;
                    updatePriceSummary();
                }
            });

            quantityInput.addEventListener('change', (e) => {
                const value = parseInt(e.target.value);
                if (value >= 1 && value <= 10) {
                    quantity = value;
                    updatePriceSummary();
                } else {
                    e.target.value = quantity;
                }
            });

            // 加入購物車
            addToCartBtn.addEventListener('click', addToCart);

            // 手機版加入購物車
            mobileAddBtn.addEventListener('click', addToCart);

            // 購物車按鈕
            cartBtn.addEventListener('click', showCart);
        }

        // 加入購物車
        function addToCart() {
            if (!selectedBase) return;

            const selectedToppingProducts = selectedToppings.map(id =>
                products.find(p => p.id === id)
            );

            const cartItem = {
                id: Date.now(), // 臨時 ID
                baseId: selectedBase.id,
                baseName: selectedBase.name,
                basePrice: parseFloat(selectedBase.price),
                toppings: selectedToppingProducts.map(t => ({
                    id: t.id,
                    name: t.name,
                    price: parseFloat(t.price)
                })),
                quantity: quantity,
                totalPrice: (parseFloat(selectedBase.price) +
                           selectedToppings.reduce((sum, id) => {
                               const topping = products.find(p => p.id === id);
                               return sum + parseFloat(topping.price);
                           }, 0)) * quantity
            };

            cart.push(cartItem);
            localStorage.setItem('cart', JSON.stringify(cart));

            // 重置選擇
            resetSelection();
            updateCartDisplay();

            // 顯示成功訊息
            showMessage('已加入購物車！', 'success');
        }

        // 重置選擇
        function resetSelection() {
            selectedBase = null;
            selectedToppings = [];
            quantity = 1;
            quantityInput.value = 1;

            document.querySelectorAll('.base-card').forEach(card => {
                card.classList.remove('selected');
            });

            document.querySelectorAll('.topping-card').forEach(card => {
                card.classList.remove('selected');
            });

            updatePriceSummary();
            updateAddToCartButton();
        }

        // 更新購物車顯示
        function updateCartDisplay() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

            if (totalItems > 0) {
                cartCount.textContent = totalItems;
                cartCount.classList.remove('hidden');
            } else {
                cartCount.classList.add('hidden');
            }
        }

        // 顯示購物車
        function showCart() {
            if (cart.length === 0) {
                showMessage('購物車是空的', 'info');
                return;
            }

            // 跳轉到結帳頁面
            window.location.href = 'checkout.html';
        }

        // 顯示訊息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success)' :
                           type === 'error' ? 'var(--danger)' : 'var(--info)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 300);
            }, 3000);
        }

        // 添加動畫樣式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
