# 🍦 冰淇淋店管理系統 - 快速啟動指南

## 系統啟動步驟

### 1️⃣ 進入後端目錄
```bash
cd backend
```

### 2️⃣ 安裝依賴（首次執行）
```bash
npm install
```

### 3️⃣ 啟動伺服器
```bash
npm start
```
或
```bash
node server.js
```

伺服器將在 http://localhost:3000 啟動

### 4️⃣ 開啟網頁
- **客戶訂購頁面**: 開啟 `customer-app.html`
- **管理員登入**: 開啟 `index.html`，點擊 Logo 5次
  - 預設帳號：`admin`
  - 預設密碼：`admin123`

## 🚀 已完成功能

### ✅ 第一階段：資料庫遷移
- SQLite 資料庫
- Sequelize ORM
- 自動建立預設管理員

### ✅ 第二階段：管理員系統
- 隱藏入口（Logo 點擊5次）
- JWT 認證
- 響應式管理後台

### ✅ 第三階段：產品管理
- 多圖片上傳（最多5張）
- 庫存管理與警告
- 優惠系統（數量/百分比/固定金額）
- 40種預設產品（10基底+30配料）

### ✅ 第四階段：訂單系統
- 7-11門市整合（22個模擬門市）
- Excel匯出（DOC{年}{日}{月}.xlsx）
- 訂單編號格式：ORD{年}{日}{月}{時}{分}{秒}{隨機3碼}
- 完整訂單管理介面

### ✅ 第五階段：Telegram通知
- 新訂單即時通知
- 狀態更新通知
- 每日營業報告
- 配置管理介面

### ✅ 第六階段：公告系統
- 管理員可新增/編輯/刪除公告
- 支援4種公告類型（資訊/警告/成功/優惠）
- 優先級排序
- 日期範圍控制
- 客戶端自動顯示活躍公告

## 📝 測試流程

### 客戶端測試
1. 開啟 `customer-app.html`
2. 選擇基底口味（必選）
3. 選擇配料（可選多個）
4. 加入購物車
5. 填寫訂單資訊
6. 選擇7-11門市
7. 確認訂購

### 管理端測試
1. 開啟 `index.html`
2. 點擊 Logo 5次進入登入頁
3. 使用 admin/admin123 登入
4. 測試各項功能：
   - 產品管理（新增/編輯/刪除）
   - 訂單管理（查看/更新狀態/匯出Excel）
   - 公告管理（新增/編輯/刪除/排序）
   - Telegram設定（配置Token/ChatID）

## ⚠️ 注意事項

1. **Telegram Bot設定**：
   - 需要先建立 Telegram Bot
   - 取得 Bot Token 和 Chat ID
   - 在管理後台配置

2. **資料庫**：
   - 使用 SQLite，資料存在 `backend/database/ice_cream_shop.db`
   - 首次啟動會自動建立資料庫

3. **圖片上傳**：
   - 上傳的圖片存在 `backend/uploads/`
   - 自動轉換為 WebP 格式
   - 自動生成縮圖

## 🔧 常見問題

### Q: 無法啟動伺服器？
A: 確認已安裝 Node.js，並在 backend 目錄執行 npm install

### Q: 找不到管理員入口？
A: 在首頁點擊 Logo（海水不可斗量）5次

### Q: 訂單無法送出？
A: 檢查伺服器是否正常運行（http://localhost:3000）

### Q: Telegram通知未收到？
A: 檢查 Bot Token 和 Chat ID 是否正確配置

## 📞 技術支援

如有問題，請檢查：
1. 伺服器控制台的錯誤訊息
2. 瀏覽器開發者工具的 Console
3. 網路請求是否正常（Network 面板） 