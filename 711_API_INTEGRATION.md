# 🏪 7-11 門市選擇 API 整合說明

## 概述

本系統已整合真實的 7-11 門市選擇 API，使用 [7-11 電子地圖](https://emap.presco.com.tw) 服務，讓客戶可以方便地選擇取貨門市。

## 整合架構

### 1. API 端點

- **選擇門市頁面**: `https://emap.presco.com.tw/c2cemap.ashx`
- **回調端點**: `http://localhost:3000/api/seven-eleven/callback`

### 2. 參數說明

| 參數 | 值 | 說明 |
|------|-----|------|
| eshopid | 870 | 電商 ID |
| servicetype | 1 | 服務類型（1=取貨） |
| url | callback URL | 選擇完成後的回調地址 |

### 3. 回調資料格式

7-11 API 會以 POST 方法回傳以下資料：

```
CVSStoreID: 門市代號
CVSStoreName: 門市名稱  
CVSAddress: 門市地址
CVSTelephone: 門市電話
CVSOutSide: 是否為外縣市（1=是，0=否）
ExtraData: 額外資料
```

## 使用流程

### 客戶端

1. 點擊「選擇 7-11 門市」按鈕
2. 開啟新視窗顯示 7-11 門市地圖
3. 選擇想要的門市
4. 系統自動接收並顯示選中的門市資訊
5. 提交訂單時包含門市資訊

### 技術實現

1. **開啟門市選擇**
```javascript
window.openSevenElevenMap = function() {
    const mapUrl = `https://emap.presco.com.tw/c2cemap.ashx?eshopid=870&servicetype=1&url=${encodeURIComponent(callbackUrl)}`;
    window.open(mapUrl, 'SevenElevenMap', 'width=800,height=600');
}
```

2. **接收選擇結果**
```javascript
window.addEventListener('message', function(event) {
    if (event.data.type === 'seven-eleven-selected') {
        // 處理門市資訊
    }
});
```

3. **回調處理**
- 伺服器接收 POST 資料
- 返回成功頁面
- 使用 postMessage 通知父視窗

## 注意事項

1. **HTTPS 要求**: 生產環境必須使用 HTTPS
2. **CORS 設定**: 已在路由中啟用 CORS
3. **視窗通訊**: 使用 postMessage 進行跨視窗通訊
4. **資料驗證**: 務必驗證回傳的門市資料

## 測試方式

### 本地測試

1. 啟動伺服器：`cd backend && npm start`
2. 開啟客戶端：`customer-app.html`
3. 進入結帳流程
4. 點擊「選擇 7-11 門市」
5. 選擇門市並確認資訊正確顯示

### 生產環境

1. 確保使用 HTTPS
2. 更新回調 URL 為正式網址
3. 測試跨域設定是否正確

## 故障排除

### 無法開啟門市選擇視窗
- 檢查瀏覽器是否阻擋彈出視窗
- 確認 URL 參數正確

### 選擇門市後沒有反應
- 檢查 console 是否有錯誤訊息
- 確認 postMessage 監聽器正常運作
- 驗證回調 URL 是否可訪問

### 門市資訊顯示不正確
- 檢查欄位對應是否正確
- 確認字元編碼（UTF-8）

## 相關檔案

- 後端路由：`backend/routes/sevenEleven.js`
- 客戶端：`customer-app.html`
- 訂單 API：`backend/routes/orders.js`

## 未來優化

1. 加入門市篩選功能（依距離、營業時間等）
2. 整合門市庫存查詢
3. 支援其他便利商店（全家、萊爾富等）
4. 優化手機版選擇體驗 