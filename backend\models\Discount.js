const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Discount = sequelize.define('discount', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '優惠名稱'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '優惠說明'
  },
  type: {
    type: DataTypes.ENUM('quantity', 'bundle', 'percentage', 'fixed'),
    allowNull: false,
    comment: '優惠類型：quantity=數量優惠, bundle=組合優惠, percentage=百分比折扣, fixed=固定折扣'
  },
  rules: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    comment: '優惠規則JSON，例如：{min_quantity: 3, discount_percent: 10}'
  },
  product_ids: {
    type: DataTypes.JSON,
    defaultValue: [],
    comment: '適用的產品ID列表'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  }
});

// 檢查優惠是否有效
Discount.prototype.isValid = function() {
  const now = new Date();
  if (!this.is_active) return false;
  if (this.start_date && now < this.start_date) return false;
  if (this.end_date && now > this.end_date) return false;
  return true;
};

module.exports = Discount; 